using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using CSG.Common.ExtensionMethods;
using DBUtils;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractionSegments;
using PartAttribs = GenesysCloudDefParticipantAttrib;
using Newtonsoft.Json;
using StandardUtils;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CallSumm = GenesysCloudDefCallSummary;
using GenesysCloudUtils;

#nullable enable

namespace GenesysCloudUtils
{
    public class DetailData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime DetailInteractionLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string? TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private DataTable ConversationSummaryData;
        private CSG.Adapter.Configuration.RegexReplacement[]? _renameParticipantAttributeNames;
        private readonly ILogger? _logger;
        private readonly object _detailInteractionLock = new object();
        private readonly object _participantAttributeLock = new object();
        private readonly object _participantSummaryLock = new object();
        private readonly object _flowOutcomesLock = new object();

        public DetailData(
            ILogger? logger,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            _logger = logger;
            _renameParticipantAttributeNames = renameParticipantAttributeNames;

            GCUtilities.Initialize();
            DBUtil.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            ConversationSummaryData = DBUtil.CreateInMemTable("convsummarydata");
        }


        private string RenameParticipantAttributeNames(
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            string attributeName)
        {
            if (_renameParticipantAttributeNames == null || _renameParticipantAttributeNames.Length == 0)
                return attributeName;

            foreach (var rule in _renameParticipantAttributeNames)
            {
                if (string.IsNullOrEmpty(rule.Find))
                {
                    _logger?.LogWarning("Empty RenameParticipantAttributeNames option Find value");
                    continue;
                }
                var pre = attributeName;
                attributeName = Regex.Replace(attributeName, rule.Find, rule.Replace ?? "");
                if (string.IsNullOrEmpty(attributeName))
                    throw new ArgumentNullException(pre, $"Attribute name '{pre}' was cleared by rule '{rule}'.");

                if (pre != attributeName)
                {
                    _logger?.LogDebug("Attribute '{0}' renamed to '{1}'", pre, attributeName);
                    return attributeName;
                }
            }

            return attributeName;
        }

        public class JobCompletionResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        private async Task<JobCompletionResult> WaitForJobCompletionViaPollingAsync(string uri, string jobId)
        {
            var result = new JobCompletionResult();

            try
            {
                _logger?.LogInformation("Polling for job {JobId} status", jobId);

                // Set up a timeout - 15 minutes should be enough for most jobs
                var timeout = TimeSpan.FromMinutes(15);
                var startTime = DateTime.UtcNow;
                var endTime = startTime.Add(timeout);

                // Poll interval - start with 2 seconds, then increase
                var pollInterval = TimeSpan.FromSeconds(2);
                var maxPollInterval = TimeSpan.FromSeconds(10);

                // Wait 3 seconds before the first poll
                _logger?.LogDebug("Waiting 3 seconds before first poll for job {JobId}", jobId);
                await Task.Delay(TimeSpan.FromSeconds(3));

                // Poll for job status until it completes or times out
                while (DateTime.UtcNow < endTime)
                {
                    _logger?.LogInformation("Checking status of job {JobId}", jobId);
                    string jobStatusJson = JsonActions.JsonReturnString(uri + $"/api/v2/analytics/conversations/details/jobs/{jobId}", GCApiKey);

                    if (string.IsNullOrWhiteSpace(jobStatusJson))
                    {
                        // This could be a 202 response (job still processing) - check the response code
                        if (JsonActions.responseCode == "Accepted")
                        {
                            var elapsed = DateTime.UtcNow - startTime;
                            _logger?.LogInformation("Job {JobId} still processing (HTTP 202 Accepted) - elapsed: {Elapsed:mm\\:ss}, next check in {Interval:F1}s",
                                jobId, elapsed, pollInterval.TotalSeconds);
                        }
                        else
                        {
                            _logger?.LogWarning("Empty response when checking job {JobId} status - response code: {ResponseCode}", jobId, JsonActions.responseCode ?? "Unknown");
                        }
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    if (jobStatusJson.Contains("\"error\": true"))
                    {
                        _logger?.LogWarning("Error response when checking job {JobId} status: {Response}", jobId, jobStatusJson);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    var jobStatus = JsonConvert.DeserializeObject<DetInt.ReportJobStatus>(jobStatusJson,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (jobStatus == null)
                    {
                        _logger?.LogWarning("Failed to deserialize job status for job {JobId}", jobId);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    _logger?.LogInformation("Job {JobId} status: {State}", jobId, jobStatus.state);

                    // Check if the job has completed
                    if (jobStatus.state == "FULFILLED" || jobStatus.state == "SUCCESS" || jobStatus.state == "COMPLETE")
                    {
                        _logger?.LogInformation("Job {JobId} completed successfully with state: {State}", jobId, jobStatus.state);
                        result.Success = true;
                        return result;
                    }

                    // Check if the job has failed
                    if (jobStatus.state == "FAILED" || jobStatus.state == "ERROR")
                    {
                        string errorMessage = $"Job failed with state: {jobStatus.state}";

                        _logger?.LogError("Job {JobId} failed: {ErrorMessage}", jobId, errorMessage);
                        result.ErrorMessage = errorMessage;
                        return result;
                    }

                    // Job is still running, wait and check again
                    _logger?.LogInformation("Job {JobId} is still running with state: {State}, checking again in {Interval} seconds",
                        jobId, jobStatus.state, pollInterval.TotalSeconds);

                    await Task.Delay(pollInterval);

                    // Increase poll interval for next iteration (up to max)
                    pollInterval = TimeSpan.FromMilliseconds(Math.Min(pollInterval.TotalMilliseconds * 1.5, maxPollInterval.TotalMilliseconds));
                }

                // If we get here, the job has timed out
                result.ErrorMessage = $"Timeout waiting for job to complete after {timeout.TotalMinutes} minutes";
                _logger?.LogError("Timeout waiting for job {JobId} to complete after {Timeout} minutes", jobId, timeout.TotalMinutes);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Error waiting for job completion: {ex.Message}";
                _logger?.LogError(ex, "Error waiting for job {JobId} completion", jobId);
            }

            return result;
        }

        public DataSet GetDetailInteractionDataFromGC(string SyncType, String StartDate, String EndDate)
        {
            return GetDetailInteractionDataFromGCAsync(SyncType, StartDate, EndDate).GetAwaiter().GetResult();
        }

        public DataSet GetDetailInteractionDataFromGC(string SyncType, String StartDate, String EndDate, TimeSpan? lookBackSpan)
        {
            return GetDetailInteractionDataFromGCAsync(SyncType, StartDate, EndDate, lookBackSpan).GetAwaiter().GetResult();
        }

        private async Task<DataSet> GetDetailInteractionDataFromGCAsync(string SyncType, String StartDate, String EndDate, TimeSpan? lookBackSpan = null)
        {
            Console.WriteLine("Initiating data retrieval job...");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/jobs/availability", GCApiKey);

            JobDateLimit DateMax;
            if (string.IsNullOrEmpty(JsonString))
            {
                _logger?.LogWarning("Failed to retrieve data availability information from API, using current time as fallback");
                DateMax = new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
            }
            else
            {
                try
                {
                    DateMax = JsonConvert.DeserializeObject<JobDateLimit>(JsonString,
                                             new JsonSerializerSettings
                                             {
                                                 NullValueHandling = NullValueHandling.Ignore
                                             }) ?? new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
                }
                catch (JsonException ex)
                {
                    _logger?.LogError(ex, "Failed to deserialize data availability response, using current time as fallback. Response: {Response}",
                        JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                    DateMax = new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
                }
            }

            // Parse with DateTimeStyles.AdjustToUniversal to properly handle the 'Z' suffix without double conversion
            DateTime FromDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DateTime ToDate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal);

            Console.WriteLine($"Data fetch parameters:\n- Start date: {StartDate}\n- End date: {EndDate}\n- From date: {FromDate}\n- To date: {ToDate}\n- Data availability date: {DateMax.dataAvailabilityDate}\n- Current time (UTC): {DateTime.UtcNow}");

            bool RunJob = false;
            bool RunQuery = false;

            if (FromDate < DateMax.dataAvailabilityDate)
            {
                Console.WriteLine("\nInitiating job to retrieve complete or partial data set");
                RunJob = true;

                if (ToDate < DateMax.dataAvailabilityDate)
                {
                    Console.WriteLine("\nEnd date is within data availability window - can retrieve all data with a single job");
                    RunQuery = false;
                }
                else
                {
                    Console.WriteLine("\nEnd date exceeds data availability window - adjusting end date and using partial query for remaining data");
                    RunQuery = true;
                    ToDate = DateMax.dataAvailabilityDate;
                }
            }
            else
            {
                RunJob = false;
                RunQuery = true;
            }

            DataSet DSDetailInteraction = new DataSet();

            if (RunJob)
            {
                Console.WriteLine("\nExecuting data retrieval job");
                DSDetailInteraction = await GetDetailInteractionDataFromGCJob(
                    FromDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"),
                    ToDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"));

                if (RunQuery)
                {
                    DateTime maxDate = DateMax.dataAvailabilityDate;
                    DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(maxDate.AddMinutes(-30).ToString("yyyy-MM-ddTHH:mm:00.000Z"), EndDate);
                    DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                    MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
                }
            }
            else
            {
                DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(StartDate, EndDate);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[0].Copy());
                DSDetailInteraction.Tables.Add(TempAttribs);
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[1].Copy());
                DSDetailInteraction.Tables.Add(TempDataSet.Tables[2].Copy());
            }

            DataTable OutConversations = DBUtil.GetSQLTableData("select conversationid from convsummarydata where conversationenddateltc is null and firstmediatype = 'voice'", "ouytconversations");
            _logger?.LogInformation("Found {OutstandingCount} outstanding voice conversations to process", OutConversations.Rows.Count);

            if (OutConversations.Rows.Count > 0)
            {
                _logger?.LogInformation("Processing outstanding conversations");
                DataSet TempDataSet = await GetOutStandingConversations(OutConversations, lookBackSpan);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
            }

            Console.Write("\n");
            if (DSDetailInteraction.Tables.Count > 0)
            {
                DSDetailInteraction.Tables.Add(GetInteractionSummary(DSDetailInteraction.Tables[0]));
            }

            Console.WriteLine("Returning Data to Calling Method");
            return DSDetailInteraction;
        }

        private void MergeDataSets(DataSet DSDetailInteraction, DataSet TempDataSet, DataTable? TempAttribs)
        {
            // Check if both datasets have the required tables
            if (DSDetailInteraction.Tables.Count < 1 || TempDataSet.Tables.Count < 1)
            {
                Console.WriteLine("Warning: One of the datasets doesn't have enough tables for merging. DSDetailInteraction has {0} tables, TempDataSet has {1} tables.",
                    DSDetailInteraction.Tables.Count, TempDataSet.Tables.Count);
                return;
            }

            // Process the first table (detailed interaction data)
            foreach (DataRow TempInt in TempDataSet.Tables[0].Rows)
            {
                try
                {
                    if (DSDetailInteraction.Tables[0].Select("keyid = '" + TempInt["keyid"] + "'").Length == 0)
                    {
                        DSDetailInteraction.Tables[0].ImportRow(TempInt);
                        // _logger?.LogDebug("Added new interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    }
                    else
                    {
                        DataRow DetailedRowTemp = DSDetailInteraction.Tables[0].Select("keyid = '" + TempInt["keyid"] + "'").FirstOrDefault();
                        foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[0].Columns)
                        {
                            if (!DetailedColumn.ReadOnly && DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempInt[DetailedColumn.ColumnName].ToString())
                                DetailedRowTemp[DetailedColumn.ColumnName] = TempInt[DetailedColumn.ColumnName];
                        }
                        // _logger?.LogDebug("Updated existing interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    }
                }
                catch (Exception e)
                {
                    _logger?.LogDebug("Failed to process interaction record with keyid: {KeyId}", TempInt["keyid"]);
                    Console.WriteLine("Exception caught in Detailed Interaction Merge.\nError Message: {0}\nInner Exception: {1}", e.ToString(), e.InnerException);
                }
            }

            // Process participant attributes if available
            if (TempAttribs != null && DSDetailInteraction.Tables.Count >= 2)
            {
                DataColumnCollection columns = DSDetailInteraction.Tables[1].Columns;
                foreach (DataColumn AttribCol in TempAttribs.Columns)
                {
                    if (!columns.Contains(AttribCol.ColumnName))
                    {
                        DSDetailInteraction.Tables[1].Columns.Add(AttribCol.ColumnName, AttribCol.DataType);
                        _logger?.LogDebug("Added new attribute column: {ColumnName}", AttribCol.ColumnName);
                    }
                }

                int addedCount = 0, updatedCount = 0, errorCount = 0;

                foreach (DataRow TempAttrib in TempAttribs.Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[1].Select("keyid = '" + TempAttrib["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[1].ImportRow(TempAttrib);
                            addedCount++;
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[1].Select("keyid = '" + TempAttrib["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[1].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempAttrib.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempAttrib[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempAttrib[DetailedColumn.ColumnName];
                                }
                            }
                            updatedCount++;
                        }
                    }
                    catch (Exception e)
                    {
                        errorCount++;
                        _logger?.LogError(e, "Exception caught in participant attributes merge module for keyid {KeyId}: {ErrorMessage}",
                            TempAttrib["keyid"], e.Message);
                    }
                }

                // Smart summary log instead of repetitive ATD: entries
                if (addedCount > 0 || updatedCount > 0 || errorCount > 0)
                {
                    _logger?.LogDebug("Participant attributes merge: {Added} added, {Updated} updated, {Errors} errors",
                        addedCount, updatedCount, errorCount);
                    Console.Write($"PA[+{addedCount}*{updatedCount}!{errorCount}]");
                }
            }

            // Process participant summary data if available
            if (TempDataSet.Tables.Count >= 2 && DSDetailInteraction.Tables.Count >= 3)
            {
                int addedCount = 0, updatedCount = 0, errorCount = 0;

                foreach (DataRow TempPartSumm in TempDataSet.Tables[1].Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[2].Select("keyid = '" + TempPartSumm["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[2].ImportRow(TempPartSumm);
                            addedCount++;
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[2].Select("keyid = '" + TempPartSumm["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[2].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempPartSumm.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempPartSumm[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempPartSumm[DetailedColumn.ColumnName];
                                }
                            }
                            updatedCount++;
                        }
                    }
                    catch (Exception e)
                    {
                        errorCount++;
                        _logger?.LogError(e, "Exception caught in participant summary merge module for keyid {KeyId}: {ErrorMessage}",
                            TempPartSumm["keyid"], e.Message);
                    }
                }

                // Smart summary log instead of repetitive PSA: entries
                if (addedCount > 0 || updatedCount > 0 || errorCount > 0)
                {
                    _logger?.LogDebug("Participant summary merge: {Added} added, {Updated} updated, {Errors} errors",
                        addedCount, updatedCount, errorCount);
                    Console.Write($"PS[+{addedCount}*{updatedCount}!{errorCount}]");
                }
            }

            // Process flow outcome data if available
            if (TempDataSet.Tables.Count >= 3 && DSDetailInteraction.Tables.Count >= 4)
            {
                int addedCount = 0, updatedCount = 0, errorCount = 0;

                foreach (DataRow TempFlowOutcome in TempDataSet.Tables[2].Rows)
                {
                    try
                    {
                        if (DSDetailInteraction.Tables[3].Select("keyid = '" + TempFlowOutcome["keyid"] + "'").Length == 0)
                        {
                            DSDetailInteraction.Tables[3].ImportRow(TempFlowOutcome);
                            addedCount++;
                        }
                        else
                        {
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[3].Select("keyid = '" + TempFlowOutcome["keyid"] + "'").FirstOrDefault();
                            foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[3].Columns)
                            {
                                // Check if the column exists in the source table before accessing it
                                if (!DetailedColumn.ReadOnly && TempFlowOutcome.Table.Columns.Contains(DetailedColumn.ColumnName))
                                {
                                    if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempFlowOutcome[DetailedColumn.ColumnName].ToString())
                                        DetailedRowTemp[DetailedColumn.ColumnName] = TempFlowOutcome[DetailedColumn.ColumnName];
                                }
                            }
                            updatedCount++;
                        }
                    }
                    catch (Exception e)
                    {
                        errorCount++;
                        _logger?.LogError(e, "Exception caught in flow outcome merge module for keyid {KeyId}: {ErrorMessage}",
                            TempFlowOutcome["keyid"], e.Message);
                    }
                }

                // Smart summary log instead of repetitive FOA: entries
                if (addedCount > 0 || updatedCount > 0 || errorCount > 0)
                {
                    _logger?.LogDebug("Flow outcome merge: {Added} added, {Updated} updated, {Errors} errors",
                        addedCount, updatedCount, errorCount);
                    Console.Write($"FO[+{addedCount}*{updatedCount}!{errorCount}]");
                }
            }
        }

        private async Task<DataSet> GetOutStandingConversations(DataTable OutConversations, TimeSpan? lookBackSpan = null)
        {
            _logger?.LogInformation("Processing {ConversationCount} outstanding conversations", OutConversations.Rows.Count);

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Console.WriteLine("Creating Memory Table Detailed Interaction");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");

            Console.WriteLine("Creating Memory Table Participant Summary Data");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Creating Memory Table Flow Outcome Data");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            int MaxRowsToSend = 75;
            int currentPage = 1;

            int totalPages = (OutConversations.Rows.Count % MaxRowsToSend == 0) ?
                (OutConversations.Rows.Count / MaxRowsToSend) :
                (OutConversations.Rows.Count / MaxRowsToSend) + 1;

            while (currentPage <= totalPages)
            {
                Console.WriteLine("Working On Batch Page : {0}", currentPage);
                StringBuilder SelectString = new StringBuilder("");
                DataTable dtTemp = OutConversations.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = OutConversations.TableName;

                foreach (DataRow DRRow in dtTemp.Rows)
                {
                    // Processing conversation - using debug level to avoid log flooding
                    SelectString.Append(DRRow["conversationid"] + ",");
                }

                SelectString.Length = SelectString.Length - 1;

                Console.Write("#");

                // Refresh API token every 275 requests to prevent rate limiting
                if ((currentPage % 275) == 0)
                {
                    _logger?.LogDebug("Processing outstanding conversations page {Page}, refreshing API key", currentPage);
                    bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
                    GCApiKey = GCUtilities.GCApiKey;
                }

                // Use centralized rate limiting handler for outstanding conversations with robust retry logic
                string JsonString = null;
                int retryAttempts = 0;
                const int maxRetryAttempts = 3;
                bool shouldRetry = true;

                while (shouldRetry && retryAttempts <= maxRetryAttempts)
                {
                    try
                    {
                        string apiUrl = URI + "/api/v2/analytics/conversations/details?id=" + SelectString.ToString();
                        _logger?.LogDebug("Attempting to retrieve outstanding conversations batch on page {Page}, attempt {Attempt}/{MaxAttempts}. URI: {URI}",
                            currentPage, retryAttempts + 1, maxRetryAttempts + 1, apiUrl);

                        // Use the centralized JsonReturnString method which has proper rate limiting
                        JsonString = JsonActions.JsonReturnString(apiUrl, GCApiKey);

                        // Handle different response scenarios
                        if (string.IsNullOrEmpty(JsonString))
                        {
                            if (retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff: 2s, 4s, 8s
                                _logger?.LogWarning("Empty response from outstanding conversations API for page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms. URI: {URI}",
                                    currentPage, retryAttempts, maxRetryAttempts + 1, delayMs, apiUrl);
                                await Task.Delay(delayMs);
                                continue;
                            }
                            else
                            {
                                _logger?.LogError("Empty response from outstanding conversations API for page {Page} after {MaxAttempts} attempts. This may indicate no conversations to process or a persistent API issue. URI: {URI}",
                                    currentPage, maxRetryAttempts + 1, apiUrl);

                                // Check if this is a legitimate empty result (no conversations to process)
                                // If we're processing outstanding conversations and get empty results, it might be legitimate
                                _logger?.LogInformation("Treating empty response as no outstanding conversations to process for page {Page}. Continuing to next page.", currentPage);
                                shouldRetry = false;
                                break; // Exit the retry loop and continue to next page
                            }
                        }

                        // Check for structured error responses from the centralized handler
                        if (!string.IsNullOrEmpty(JsonString) && JsonString.Contains("\"error\": true"))
                        {
                            // Parse the error response to determine if it's retryable
                            bool isRetryableError = false;
                            string errorType = "Unknown";

                            try
                            {
                                var errorObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(JsonString);
                                if (errorObj?.statusCode != null)
                                {
                                    errorType = errorObj.statusCode.ToString();

                                    // Determine if this is a retryable error
                                    isRetryableError = errorType == "TooManyRequests" ||
                                                     errorType == "RequestTimeout" ||
                                                     errorType == "InternalServerError" ||
                                                     errorType == "BadGateway" ||
                                                     errorType == "ServiceUnavailable" ||
                                                     errorType == "GatewayTimeout";
                                }
                            }
                            catch (Exception parseEx)
                            {
                                _logger?.LogWarning(parseEx, "Failed to parse error response JSON for page {Page}: {Response}", currentPage, JsonString);
                            }

                            if (isRetryableError && retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff
                                _logger?.LogWarning("Retryable error ({ErrorType}) from outstanding conversations API for page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms. Response: {Response}",
                                    errorType, currentPage, retryAttempts, maxRetryAttempts + 1, delayMs, JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                                await Task.Delay(delayMs);
                                continue;
                            }
                            else
                            {
                                _logger?.LogError("Non-retryable error or max retries exceeded for outstanding conversations API on page {Page}. ErrorType: {ErrorType}, Attempts: {Attempts}. Response: {Response}",
                                    currentPage, errorType, retryAttempts + 1, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);
                                throw new InvalidOperationException($"API error for outstanding conversations batch on page {currentPage} after {retryAttempts + 1} attempts. ErrorType: {errorType}. Response: {JsonString.Substring(0, Math.Min(JsonString.Length, 500))}");
                            }
                        }

                        // If we get here, we have a successful response
                        shouldRetry = false;
                        _logger?.LogDebug("Successfully retrieved outstanding conversations batch on page {Page} after {Attempts} attempt(s)", currentPage, retryAttempts + 1);
                    }
                    catch (Exception ex)
                    {
                        if (retryAttempts < maxRetryAttempts && !(ex is UnauthorizedAccessException))
                        {
                            retryAttempts++;
                            int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff
                            _logger?.LogWarning(ex, "Exception retrieving outstanding conversations batch on page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms.",
                                currentPage, retryAttempts, maxRetryAttempts + 1, delayMs);
                            await Task.Delay(delayMs);
                            continue;
                        }
                        else
                        {
                            _logger?.LogError(ex, "Failed to retrieve outstanding conversations batch on page {Page} after {MaxAttempts} attempts. Exception type: {ExceptionType}",
                                currentPage, maxRetryAttempts + 1, ex.GetType().Name);
                            throw;
                        }
                    }
                }

                // Handle the case where we have no data to process (empty response after retries)
                if (string.IsNullOrEmpty(JsonString))
                {
                    _logger?.LogInformation("No outstanding conversations data to process for page {Page}. This may be normal if there are no outstanding conversations.", currentPage);
                    currentPage++;
                    continue; // Skip to next page
                }

                // Validate that we have meaningful JSON data to process
                if (JsonString.Length > 50)
                {
                    // Additional validation to ensure we have valid conversation data, not error JSON
                    if (JsonString.Contains("\"error\"") && JsonString.Contains("\"message\""))
                    {
                        _logger?.LogError("API returned error JSON instead of conversation data for outstanding conversations batch on page {Page}. Response: {Response}",
                            currentPage, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);
                        throw new InvalidOperationException($"API returned error JSON instead of conversation data for outstanding conversations batch on page {currentPage}. Response: {JsonString.Substring(0, Math.Min(JsonString.Length, 500))}");
                    }

                    // Validate that the JSON looks like conversation data
                    if (!JsonString.Contains("conversations") && !JsonString.Contains("\"totalHits\""))
                    {
                        _logger?.LogWarning("API response does not contain expected conversation data structure for page {Page}. Response preview: {ResponsePreview}",
                            currentPage, JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);

                        // This might be a legitimate empty result, so don't throw an exception
                        _logger?.LogInformation("Treating unexpected response structure as no conversations to process for page {Page}. Continuing to next page.", currentPage);
                        currentPage++;
                        continue;
                    }

                    Console.WriteLine("\nInteractions To Process");
                    Interactions.InteractionSegmentStruct DetailedData = null;

                    try
                    {
                        DetailedData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore,
                                           MissingMemberHandling = MissingMemberHandling.Ignore
                                       });
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger?.LogError(jsonEx, "JSON deserialization failed for outstanding conversations batch on page {Page}. This may indicate an API response format change or corrupted data. JSON response preview: {JsonPreview}",
                            currentPage, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);

                        // For outstanding conversations, if deserialization fails, we should continue to next page rather than fail completely
                        _logger?.LogWarning("Skipping page {Page} due to JSON deserialization failure. Continuing to next page to avoid blocking other conversations.", currentPage);
                        currentPage++;
                        continue;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Unexpected error during JSON deserialization for outstanding conversations batch on page {Page}. Exception type: {ExceptionType}",
                            currentPage, ex.GetType().Name);

                        // For unexpected errors, also continue to next page
                        _logger?.LogWarning("Skipping page {Page} due to unexpected deserialization error. Continuing to next page.", currentPage);
                        currentPage++;
                        continue;
                    }

                    // Check if DetailedData and conversations are not null before processing
                    if (DetailedData == null)
                    {
                        _logger?.LogWarning("DetailedData is null after JSON deserialization for outstanding conversations batch on page {Page}. This may indicate an empty or invalid response. Continuing to next page.",
                            currentPage);
                        currentPage++;
                        continue;
                    }

                    if (DetailedData.conversations == null || DetailedData.conversations.Length == 0)
                    {
                        _logger?.LogInformation("No conversations found in response for outstanding conversations batch on page {Page}. This may be normal if there are no outstanding conversations to process.",
                            currentPage);
                        currentPage++;
                        continue;
                    }

                    bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                    int conversationCount = 0;
                    int totalConversationsInBatch = DetailedData.conversations.Length;

                    _logger?.LogInformation("Processing outstanding conversations batch on page {Page}: {ConversationCount} conversations found",
                        currentPage, totalConversationsInBatch);

                    foreach (Interactions.Conversation ConvData in DetailedData.conversations)
                    {
                        if (ConvData != null)
                        {
                            conversationCount++;
                            // Log progress every 50 conversations for better monitoring
                            if (conversationCount % 50 == 0)
                            {
                                _logger?.LogInformation("Outstanding conversations processing progress on page {Page}: {ProcessedCount}/{TotalCount} conversations processed",
                                    currentPage, conversationCount, totalConversationsInBatch);
                            }
                            int PartCode = 0;
                            int SessCode = 0;
                            int SegCode = 0;
                            // Add null safety check for participants collection
                            if (ConvData.participants == null)
                            {
                                _logger?.LogWarning("Participants collection is null for conversation {ConversationId}. Skipping participant processing.", ConvData.conversationId);
                                continue;
                            }

                            foreach (Interactions.Participant ConvPart in ConvData.participants)
                            {
                                // Add null safety check for individual participant
                                if (ConvPart == null)
                                {
                                    _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.", ConvData.conversationId);
                                    continue;
                                }

                                // Add null safety check for critical participant properties
                                if (string.IsNullOrEmpty(ConvPart.participantId))
                                {
                                    _logger?.LogWarning("Participant with null or empty participantId found in conversation {ConversationId}. Skipping participant.", ConvData.conversationId);
                                    continue;
                                }

                                DataRow CheckPartExists = ParticipantSummary.Select("keyid= '" + ConvData.conversationId + "|" + ConvPart.participantId + "'").FirstOrDefault();

                                DataRow DRPartSumm = ParticipantSummary.NewRow();
                                DRPartSumm["keyid"] = ConvData.conversationId + "|" + ConvPart.participantId;
                                DRPartSumm["conversationid"] = ConvData.conversationId;
                                DRPartSumm["participantid"] = ConvPart.participantId;
                                DRPartSumm["purpose"] = ConvPart.purpose;

                                if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                {
                                    DRPartSumm["divisionid"] = ConvData.divisionIds[0];
                                }
                                else
                                {
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }

                                if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value || DRPartSumm["divisionid"] is DBNull)
                                {
                                    _logger?.LogDebug("Setting default division ID for participant summary");
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }
                                if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                    DRPartSumm["divisionid2"] = ConvData.divisionIds[1];
                                if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                    DRPartSumm["divisionid3"] = ConvData.divisionIds[2];

                                // ConvData.conversationStart is already UTC from API deserialization
                                DRPartSumm["conversationstartdate"] = ConvData.conversationStart;
                                DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart, AppTimeZone);

                                if (ConvData.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                {
                                    // ConvData.conversationEnd is already UTC from API deserialization
                                    DRPartSumm["conversationenddate"] = ConvData.conversationEnd;
                                    DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd, AppTimeZone);
                                }
                                else
                                {
                                    DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                    DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                }

                                PartCode++;
                                SessCode = 0;
                                SegCode = 0;

                                // Add null safety check for sessions collection
                                if (ConvPart.sessions == null)
                                {
                                    _logger?.LogWarning("Sessions collection is null for participant {ParticipantId} in conversation {ConversationId}. Skipping session processing.",
                                        ConvPart.participantId, ConvData.conversationId);
                                    continue;
                                }

                                foreach (Interactions.Session ConvSess in ConvPart.sessions)
                                {
                                    // Add null safety check for individual session
                                    if (ConvSess == null)
                                    {
                                        _logger?.LogWarning("Null session found for participant {ParticipantId} in conversation {ConversationId}. Skipping session.",
                                            ConvPart.participantId, ConvData.conversationId);
                                        continue;
                                    }

                                    // Add null safety check for segments collection
                                    if (ConvSess.segments == null)
                                    {
                                        _logger?.LogWarning("Segments collection is null for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment processing.",
                                            ConvPart.participantId, ConvData.conversationId);
                                        continue;
                                    }

                                    SessCode++;
                                    SegCode = 0;
                                    Interactions.Flow ConvSessFlow = ConvSess.flow;

                                    int SegmentCount = ConvSess.segments.Length;
                                    int CurrentSegment = 1;

                                    foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                                    {
                                        // Add null safety check for individual segment
                                        if (ConvSeg == null)
                                        {
                                            _logger?.LogWarning("Null segment found for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment.",
                                                ConvPart.participantId, ConvData.conversationId);
                                            continue;
                                        }

                                        SegCode++;

                                        if (!timeFieldsMillisecondResolution)
                                        {
                                            ConvSeg.segmentStart = new DateTime(
                                                ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentStart.Kind
                                            );
                                            ConvSeg.segmentEnd = new DateTime(
                                                ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentEnd.Kind
                                            );
                                        }

                                        string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();
                                        DataRow NewRow = DetailInteraction.NewRow();
                                        string TempKeyid = ConvData.conversationId + "|" + IterationCode;
                                        NewRow["keyid"] = ConvData.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);
                                        NewRow["conversationid"] = ConvData.conversationId;

                                        if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                        {
                                            NewRow["divisionid"] = ConvData.divisionIds[0];
                                        }
                                        else
                                        {
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (NewRow["divisionid"] == null || NewRow["divisionid"] == System.DBNull.Value|| NewRow["divisionid"] is DBNull)
                                        {
                                            _logger?.LogDebug("Setting default division ID for detailed interaction");
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                            NewRow["divisionid2"] = ConvData.divisionIds[1];
                                        if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                            NewRow["divisionid3"] = ConvData.divisionIds[2];

                                        // ConvData.conversationStart is already UTC from API deserialization
                                        NewRow["conversationstartdate"] = ConvData.conversationStart;
                                        NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart, AppTimeZone);

                                        if (ConvData.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                        {
                                            // ConvData.conversationEnd is already UTC from API deserialization
                                            NewRow["conversationenddate"] = ConvData.conversationEnd;
                                            NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd, AppTimeZone);
                                        }
                                        else
                                        {
                                            NewRow["conversationenddate"] = System.DBNull.Value;
                                            NewRow["conversationenddateltc"] = System.DBNull.Value;
                                        }

                                        NewRow["gencode"] = IterationCode;
                                        NewRow["peer"] = ConvSess.peerId;

                                        DateTime MaxDateTest = ConvData.conversationStart;
                                        if (MaxDateTest > DetailInteractionLastUpdate)
                                        {
                                            DetailInteractionLastUpdate = MaxDateTest;
                                            Console.Write("@");
                                        }

                                        NewRow["conversationminmos"] = decimal.Round(ConvData.mediaStatsMinConversationMos, 2);
                                        NewRow["originaldirection"] = ConvData.originatingDirection;
                                        NewRow["participantid"] = ConvPart.participantId;
                                        if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                            NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                        else
                                            NewRow["participantname"] = ConvPart.participantName;
                                        NewRow["purpose"] = ConvPart.purpose;

                                        NewRow["mediatype"] = ConvSess.mediaType;
                                        DRPartSumm["mediaType"] = ConvSess.mediaType;

                                        if (ConvSess.ani != null && ConvSess.ani.Length > 300)
                                            NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                        else
                                            NewRow["ani"] = ConvSess.ani;

                                        if (ConvSeg.queueId != null)
                                        {
                                            NewRow["queueid"] = ConvSeg.queueId;
                                            DRPartSumm["queueid"] = ConvSeg.queueId;
                                        }
                                        if (ConvPart.userId != null)
                                        {
                                            NewRow["userid"] = ConvPart.userId;
                                            DRPartSumm["userid"] = ConvPart.userId;
                                        }

                                        if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                            NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                        else
                                            NewRow["dnis"] = ConvSess.dnis;

                                        if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                        else
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis;

                                        NewRow["sessiondirection"] = ConvSess.direction;
                                        NewRow["edgeId"] = ConvSess.edgeId;
                                        if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                            NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                        else
                                            NewRow["remotedisplayable"] = ConvSess.remote;

                                        NewRow["conversationminrfactor"] = decimal.Round(ConvData.mediaStatsMinConversationRFactor, 2);

                                        if (ConvData.externalTag != null)
                                            NewRow["externalTag"] = ConvData.externalTag;

                                        // ConvSeg.segmentStart is already UTC from API deserialization
                                        var segmentStartUtc = ConvSeg.segmentStart;
                                        NewRow["segmentstartdate"] = segmentStartUtc;
                                        NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);

                                        System.TimeSpan Diff = new System.TimeSpan();

                                        if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                                        {
                                            // ConvSeg.segmentEnd is already UTC from API deserialization
                                            var segmentEndUtc = ConvSeg.segmentEnd;
                                            NewRow["segmentenddate"] = segmentEndUtc;
                                            NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                                            Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                                            NewRow["segmenttime"] = Diff.TotalSeconds;
                                            Diff = ConvSeg.segmentEnd - ConvData.conversationStart;
                                            NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                        }
                                        else
                                        {
                                            NewRow["segmentenddate"] = System.DBNull.Value;
                                            NewRow["segmenttime"] = System.DBNull.Value;
                                            NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                        }

                                        Diff = ConvSeg.segmentStart - ConvData.conversationStart;
                                        NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                                        NewRow["segmenttype"] = ConvSeg.segmentType;
                                        NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                        NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                                        string RowWrapUp = ConvSeg.wrapUpCode;
                                        string RowWrapUpNote = ConvSeg.wrapUpNote;
                                        if (RowWrapUp != null)
                                        {
                                            if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                            NewRow["wrapupcode"] = RowWrapUp;
                                            DRPartSumm["wrapupcode"] = RowWrapUp;
                                            if (RowWrapUpNote != null)
                                            {
                                                NewRow["wrapupnote"] = RowWrapUpNote;
                                                DRPartSumm["wrapupnote"] = RowWrapUpNote;
                                            }
                                            else
                                            {
                                                NewRow["wrapupnote"] = "";
                                            }
                                        }
                                        else
                                        {
                                            NewRow["wrapupcode"] = "";
                                            NewRow["wrapupnote"] = "";
                                        }

                                        if (ConvSeg.disconnectType == null)
                                            NewRow["disconnectiontype"] = "none";
                                        else
                                            NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                        NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                        NewRow["sessionprovider"] = ConvSess.provider;

                                        // Map new Session-level fields
                                        NewRow["sessionid"] = ConvSess.sessionId;
                                        NewRow["protocolcallid"] = ConvSess.protocolCallId;
                                        NewRow["remotenamedisplayable"] = ConvSess.remoteNameDisplayable;
                                        NewRow["callbackusername"] = ConvSess.callbackUserName;

                                        // Handle callback numbers array - convert to JSON string
                                        if (ConvSess.callbackNumbers != null && ConvSess.callbackNumbers.Length > 0)
                                        {
                                            NewRow["callbacknumbers"] = System.Text.Json.JsonSerializer.Serialize(ConvSess.callbackNumbers);
                                        }
                                        else
                                        {
                                            NewRow["callbacknumbers"] = DBNull.Value;
                                        }

                                        NewRow["scriptid"] = ConvSess.scriptId;
                                        NewRow["skipenabled"] = ConvertBoolean(ConvSess.skipEnabled);
                                        NewRow["timeoutseconds"] = ConvSess.timeoutSeconds;
                                        NewRow["flowouttype"] = ConvSess.flowOutType;
                                        NewRow["roomid"] = ConvSess.roomId;

                                        // Handle callback scheduled time
                                        if (ConvSess.callbackScheduledTime > DateTime.UtcNow.AddYears(-20))
                                        {
                                            NewRow["callbackscheduledtime"] = ConvSess.callbackScheduledTime;
                                        }
                                        else
                                        {
                                            NewRow["callbackscheduledtime"] = DBNull.Value;
                                        }

                                        // Map new Participant-level fields
                                        NewRow["externalcontactid"] = ConvPart.externalContactId;
                                        NewRow["externalorganizationid"] = ConvPart.externalOrganizationId;
                                        DRPartSumm["externalcontactid"] = ConvPart.externalContactId;
                                        DRPartSumm["externalorganizationid"] = ConvPart.externalOrganizationId;

                                        // Map new Segment-level fields
                                        // Handle requested routing skill IDs array - convert to JSON string
                                        if (ConvSeg.requestedRoutingSkillIds != null && ConvSeg.requestedRoutingSkillIds.Length > 0)
                                        {
                                            NewRow["requestedroutingskillids"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.requestedRoutingSkillIds);
                                        }
                                        else
                                        {
                                            NewRow["requestedroutingskillids"] = DBNull.Value;
                                        }

                                        // Handle SIP response codes array - convert to JSON string
                                        if (ConvSeg.sipResponseCodes != null && ConvSeg.sipResponseCodes.Length > 0)
                                        {
                                            NewRow["sipresponsecodes"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.sipResponseCodes);
                                        }
                                        else
                                        {
                                            NewRow["sipresponsecodes"] = DBNull.Value;
                                        }

                                        // Handle Q850 response codes array - convert to JSON string
                                        if (ConvSeg.q850ResponseCodes != null && ConvSeg.q850ResponseCodes.Length > 0)
                                        {
                                            NewRow["q850responsecodes"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.q850ResponseCodes);
                                        }
                                        else
                                        {
                                            NewRow["q850responsecodes"] = DBNull.Value;
                                        }

                                        NewRow["errorcode"] = ConvSeg.errorCode;
                                        NewRow["requestedlanguageid"] = ConvSeg.requestedLanguageId;

                                        // Map Media Endpoint Statistics fields
                                        if (ConvSess.mediaEndpointStats != null && ConvSess.mediaEndpointStats.Length > 0)
                                        {
                                            var mediaStats = ConvSess.mediaEndpointStats[0]; // Use first endpoint stats

                                            // Handle codecs array - convert to JSON string
                                            if (mediaStats.codecs != null && mediaStats.codecs.Length > 0)
                                            {
                                                NewRow["codecs"] = System.Text.Json.JsonSerializer.Serialize(mediaStats.codecs);
                                            }
                                            else
                                            {
                                                NewRow["codecs"] = DBNull.Value;
                                            }

                                            NewRow["minmos"] = mediaStats.minMos;
                                            NewRow["minrfactor"] = mediaStats.minRFactor;
                                            NewRow["maxlatencyms"] = mediaStats.maxLatencyMs;
                                            NewRow["receivedpackets"] = mediaStats.receivedPackets;
                                            NewRow["discardedpackets"] = mediaStats.discardedPackets;
                                            NewRow["overrunpackets"] = mediaStats.overrunPackets;
                                            NewRow["invalidpackets"] = mediaStats.invalidPackets;
                                            NewRow["duplicatepackets"] = mediaStats.duplicatePackets;
                                        }
                                        else
                                        {
                                            // Set all media stats fields to null if no stats available
                                            NewRow["codecs"] = DBNull.Value;
                                            NewRow["minmos"] = DBNull.Value;
                                            NewRow["minrfactor"] = DBNull.Value;
                                            NewRow["maxlatencyms"] = DBNull.Value;
                                            NewRow["receivedpackets"] = DBNull.Value;
                                            NewRow["discardedpackets"] = DBNull.Value;
                                            NewRow["overrunpackets"] = DBNull.Value;
                                            NewRow["invalidpackets"] = DBNull.Value;
                                            NewRow["duplicatepackets"] = DBNull.Value;
                                        }

                                        if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                        {
                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                            NewRow["flowname"] = ConvSessFlow.flowName;
                                            try
                                            {
                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                            }
                                            catch
                                            {
                                                NewRow["flowversion"] = 1.0;
                                            }

                                            NewRow["flowtype"] = ConvSessFlow.flowType;
                                            NewRow["exitreason"] = ConvSessFlow.exitReason;
                                            NewRow["entryreason"] = ConvSessFlow.entryReason;
                                            NewRow["entrytype"] = ConvSessFlow.entryType;
                                            NewRow["transfertype"] = ConvSessFlow.transferType;
                                            NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;
                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                            // Map new Flow-level fields
                                            NewRow["transfertargetaddress"] = ConvSessFlow.transferTargetAddress;
                                            NewRow["startinglanguage"] = ConvSessFlow.startingLanguage;
                                            NewRow["endinglanguage"] = ConvSessFlow.endingLanguage;

                                            // Use the centralized FlowOutcomeProcessor for consistent processing
                                            if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                            {
                                                try
                                                {
                                                    var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);
                                                    var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                        new[] { ConvSessFlow },
                                                        ConvData.conversationId,
                                                        ConvData.conversationStart,
                                                        ConvData.conversationEnd,
                                                        FlowOutcomes,
                                                        throwOnInvalidDates: true);

                                                    // Add "F" to status string to indicate flow outcome processing
                                                    if (flowOutcomeResult.TotalProcessed > 0)
                                                    {
                                                        Console.Write("F");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                                                        ConvData.conversationId, ex.Message);
                                                    // Continue processing other conversations
                                                }
                                            }

                                        }

                                        if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                        {
                                            foreach (Interactions.Metric ConvSessMetric in ConvSess.metrics)
                                            {
                                                string FirstChar = ConvSessMetric.name.Substring(0, 1);
                                                try
                                                {
                                                    switch (FirstChar)
                                                    {
                                                        case "n":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                            }
                                                            break;
                                                        case "t":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                    ConvSessMetric.value += 100;

                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                }
                                                                else
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                                catch (Exception e)
                                                {
                                                    Console.WriteLine("No Row For {0}\n Error: {1} \nSource {2}", ConvSessMetric.name, e.ToString(), e.Source);
                                                }
                                            }
                                        }

                                        try
                                        {
                                            DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + NewRow["keyid"] + "'").FirstOrDefault();
                                            if (CheckRowExists == null)
                                            {
                                                DetailInteraction.Rows.Add(NewRow);
                                                // Record added - using debug level to avoid log flooding
                                            }
                                            else
                                            {
                                                // Duplicate record found - using debug level to avoid log flooding
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("Exception caught in Interaction Detail Module.\nError Message: {0}\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                                            throw;
                                        }

                                        CurrentSegment++;
                                        // Segment processed - using debug level to avoid log flooding
                                    }
                                }

                                if (CheckPartExists == null)
                                {
                                    ParticipantSummary.Rows.Add(DRPartSumm);
                                    // Participant added - using debug level to avoid log flooding
                                }
                                else
                                {
                                    // Participant duplicate found - using debug level to avoid log flooding
                                }
                            }
                        }
                    }

                    // Log summary for this page
                    _logger?.LogInformation("Completed processing outstanding conversations page {Page}: {ProcessedCount}/{TotalCount} conversations processed successfully",
                        currentPage, conversationCount, totalConversationsInBatch);
                }
                else
                {
                    // This should not happen due to earlier checks, but log it just in case
                    _logger?.LogWarning("Skipping page {Page} due to insufficient JSON data (length: {JsonLength})", currentPage, JsonString?.Length ?? 0);
                }

                currentPage++;
            }

            Console.WriteLine("\nReturning {0} Row(s)", DetailInteraction.Rows.Count);

            // Log final summary of outstanding conversations processing
            _logger?.LogInformation("Outstanding conversations processing completed. Total pages processed: {TotalPages}, DetailInteraction rows: {DetailRows}, ParticipantSummary rows: {ParticipantRows}, FlowOutcomes rows: {FlowRows}",
                currentPage - 1, DetailInteraction.Rows.Count, ParticipantSummary.Rows.Count, FlowOutcomes.Rows.Count);

            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);
            return ReturnInteractionData;
        }

        public DataTable GetInteractionSummary(DataTable InteractionData)
        {
            return GetInteractionSummaryAsync(InteractionData).GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetInteractionSummaryAsync(DataTable InteractionData)
        {
            _logger?.LogInformation("Producing Conversation Summary Data");

            // Create a stopwatch to measure total processing time
            var totalStopwatch = Stopwatch.StartNew();
            var segmentStopwatch = Stopwatch.StartNew();

            // Create a table to hold unique conversation IDs
            DataTable dt = new DataTable();
            dt.Clear();
            dt.TableName = "InteractionIds";
            dt.Columns.Add("conversationId");

            // Get unique conversation IDs using HashSet for better performance and thread safety
            HashSet<string> uniqueConversationIds = new HashSet<string>();

            foreach (DataRow InteractionRow in InteractionData.Rows)
            {
                // Capture conversation ID once to avoid threading issues
                string conversationId = InteractionRow["conversationId"]?.ToString();

                // Skip if conversation ID is null or already processed
                if (string.IsNullOrEmpty(conversationId) || !uniqueConversationIds.Add(conversationId))
                {
                    continue;
                }

                DataRow InteractionIdRow = dt.NewRow();
                InteractionIdRow["conversationId"] = conversationId;
                dt.Rows.Add(InteractionIdRow);
            }

            _logger?.LogInformation("Found {Count} unique conversations to process", dt.Rows.Count);

            // Since all data is already in memory, we can process in parallel
            // but need to be careful with thread safety
            var syncLock = new object();
            int CounterCSRows = 0;

            // Create batches of conversations to process in parallel
            // Use a larger batch size to reduce the number of tasks
            int batchSize = 500;
            var batches = new List<List<DataRow>>();

            for (int i = 0; i < dt.Rows.Count; i += batchSize)
            {
                var batch = new List<DataRow>();
                for (int j = i; j < Math.Min(i + batchSize, dt.Rows.Count); j++)
                {
                    batch.Add(dt.Rows[j]);
                }
                batches.Add(batch);
            }

            // Limit the number of concurrent tasks to avoid thread contention
            // This is similar to how it's done in the interaction processing
            int maxConcurrentTasks = Math.Min(Environment.ProcessorCount, 4); // Use at most 4 threads or number of CPU cores, whichever is less
            _logger?.LogInformation("Processing with maximum {MaxThreads} concurrent threads", maxConcurrentTasks);

            // Process batches with limited concurrency
            var semaphore = new SemaphoreSlim(maxConcurrentTasks);
            var tasks = new List<Task>();

            foreach (var batch in batches)
            {
                await semaphore.WaitAsync();

                tasks.Add(Task.Run(async () => {
                    try
                    {
                        // Process each conversation in this batch
                        foreach (DataRow InteractionRow in batch)
                        {
                            // Capture conversation ID once to avoid threading issues
                            string conversationId = InteractionRow["conversationId"]?.ToString();
                            if (string.IsNullOrEmpty(conversationId))
                            {
                                continue;
                            }

                            // Create a filtered table with just this conversation's data using LINQ for safety
                            DataTable InteractDetails = InteractionData.Clone();
                            var filteredRows = InteractionData.AsEnumerable()
                                .Where(row => row.Field<string>("conversationid") == conversationId);

                            foreach (DataRow DRInteraction in filteredRows)
                            {
                                InteractDetails.ImportRow(DRInteraction);
                            }

                            // Process this conversation
                            lock (syncLock) // Lock when calling ConvSummary to avoid thread contention on ConversationSummaryData
                            {
                                ConvSummary(InteractDetails);
                            }

                            // Thread-safe increment of counter
                            int localCount;
                            lock (syncLock)
                            {
                                CounterCSRows++;
                                localCount = CounterCSRows;
                            }

                            // Log progress every 100 rows
                            if (localCount % 100 == 0)
                            {
                                _logger?.LogDebug("Conversation summary processing: {RowCount} of {TotalRows} rows processed",
                                    localCount, dt.Rows.Count);
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }

            // Wait for all tasks to complete
            await Task.WhenAll(tasks);

            _logger?.LogInformation("Processed all {Count} conversation summaries in {ElapsedSeconds:F2} seconds",
                CounterCSRows, totalStopwatch.Elapsed.TotalSeconds);

            return ConversationSummaryData;
        }

        private Boolean ConvSummary(DataTable Conversation)
        {
            DataView view = new DataView(Conversation);
            view.Sort = "segmentstartdate asc";

            if (Conversation.Rows.Count > 0)
            {
                DataRow ConvSummaryRow = ConversationSummaryData.NewRow();

                decimal TalkTime = 0;
                decimal WrapUpTime = 0;
                decimal QueueTime = 0;
                DateTime LastSegmentEnd = (DateTime)Conversation.Rows[0]["conversationstartdate"];
                string ANI = Conversation.Rows[0]["ani"].ToString();
                string DNIS = Conversation.Rows[0]["dnis"].ToString();

                string FirstAgentId = string.Empty;
                string LastAgentId = string.Empty;

                string FirstQueueId = string.Empty;
                string LastQueueId = string.Empty;

                string LastPurpose = string.Empty;
                string LastDisconnect = string.Empty;

                string FirstWrapUpCode = string.Empty;
                string LastWrapUpCode = string.Empty;

                string LastPeer = string.Empty;

                bool FirstAgent = false;
                bool FirstWrapUp = false;
                bool FirstQueue = false;

                int TotalHold = 0;
                decimal TotalHoldTime = 0;

                string LastMediaType = Conversation.Rows[0]["mediatype"].ToString();
                int TotalColdTrans = 0;
                int TotalWarmTrans = 0;
                int HandleCount = 0;
                decimal TotalHandleTime = 0;
                int AnswerCount = 0;
                decimal TotalAnswerTime = 0;

                int AbandonCount = 0;

                int ResponseCount = 0;
                decimal TotalResponseTime = 0;

                bool CallBackDetected = false;
                int CallBackPart = 0;

                decimal LastSegmentTime = 0;

                foreach (DataRow Segment in Conversation.Rows)
                {
                    if (Segment["purpose"].ToString() == "agent" || Segment["purpose"].ToString() == "acd")
                    {
                        string[] GenCodeSplit = Segment["gencode"].ToString().Split('|');

                        if (Segment["mediatype"].ToString() == "callback")
                        {
                            CallBackDetected = true;
                            CallBackPart = int.Parse(GenCodeSplit[0]);
                        }

                        if (Segment["segmenttype"].ToString() == "wrapup")
                        {
                            if (!FirstWrapUp)
                            {
                                FirstWrapUp = true;
                                FirstWrapUpCode = Segment["wrapupcode"].ToString();
                            }

                            LastWrapUpCode = Segment["wrapupcode"].ToString();
                        }

                        if (Segment["tabandon"] != null && Segment["tabandon"] != System.DBNull.Value && float.TryParse(Segment["tabandon"].ToString(), out float tabandonValue) && tabandonValue > 0)
                            AbandonCount = AbandonCount + 1;

                        if (Segment["peer"] != null && Segment["peer"].ToString() != "")
                            LastPeer = Segment["peer"].ToString();

                        if (CallBackDetected == true && int.Parse(GenCodeSplit[0]) == CallBackPart && Segment["mediatype"].ToString() != "callback")
                        {
                        }
                        else
                        {
                            if (Segment["segmenttype"].ToString() == "interact")
                            {
                                LastMediaType = Segment["mediatype"].ToString();

                                if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                                {
                                    if (Segment["purpose"].ToString() == "agent")
                                    {
                                        TalkTime += Convert.ToDecimal(Segment["segmenttime"]);
                                        LastAgentId = Segment["userid"].ToString();
                                        LastQueueId = Segment["queueid"].ToString();

                                        if (!FirstAgent)
                                        {
                                            FirstAgentId = Segment["userid"].ToString();
                                            FirstAgent = true;
                                        }
                                    }
                                    else
                                    {
                                        QueueTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }

                                    LastSegmentEnd = (DateTime)Segment["segmentenddate"];
                                }

                                if (!FirstQueue)
                                {
                                    FirstQueueId = Segment["queueid"].ToString();
                                    LastQueueId = Segment["queueid"].ToString();
                                    FirstQueue = true;
                                }
                                else
                                {
                                    LastQueueId = Segment["queueid"].ToString();
                                }
                            }

                            if (Segment["segmenttype"].ToString() == "wrapup")
                            {
                                if (Segment["segmenttime"] != null  && Segment["segmenttime"] != System.DBNull.Value)
                                {
                                    if (Convert.ToInt32(Segment["segmenttime"]) == 0)
                                    {
                                        decimal TempWrapUpTime = Convert.ToDecimal((LastSegmentEnd - (DateTime)Segment["segmentstartdate"]).TotalSeconds);
                                        WrapUpTime += TempWrapUpTime;
                                        TalkTime = TalkTime - TempWrapUpTime;
                                    }
                                    else
                                    {
                                        WrapUpTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }
                                }
                            }

                            if (Segment["ani"] != null && Segment["ani"] != System.DBNull.Value)
                            {
                                if (Conversation.Rows[0]["mediatype"].ToString() == "email" || Conversation.Rows[0]["mediatype"].ToString() == "chat")
                                    ANI = Segment["remotedisplayable"].ToString();
                            }

                            if (Segment["theldcomplete"] != null && Segment["theldcomplete"] != System.DBNull.Value && Segment["theldcomplete"].ToString() != "")
                                TotalHoldTime += Convert.ToDecimal(Segment["theldcomplete"]);

                            if (Segment["nconsulttransferred"] != null && Segment["nconsulttransferred"] != System.DBNull.Value && Segment["nconsulttransferred"].ToString() != "")
                                TotalWarmTrans += int.Parse(Segment["nconsulttransferred"].ToString());

                            if (Segment["nblindtransferred"] != null && Segment["nblindtransferred"] != System.DBNull.Value && Segment["nblindtransferred"].ToString() != "")
                                TotalColdTrans += int.Parse(Segment["nblindtransferred"].ToString());

                            if (Segment["thandle"] != null && Segment["thandle"] != System.DBNull.Value && Segment["thandle"].ToString() != "")
                            {
                                HandleCount += 1;
                                TotalHandleTime += Convert.ToDecimal(Segment["thandle"]);
                            }

                            if (Segment["tanswered"] != null && Segment["tanswered"] != System.DBNull.Value && Segment["tanswered"].ToString() != "")
                            {
                                AnswerCount += 1;
                                TotalAnswerTime += Convert.ToDecimal(Segment["tanswered"]);
                            }

                            if (Segment["tagentresponsetime"] != null && Segment["tagentresponsetime"] != System.DBNull.Value && Segment["tagentresponsetime"].ToString() != "")
                            {
                                ResponseCount += 1;
                                TotalResponseTime += Convert.ToDecimal(Segment["tagentresponsetime"]);
                            }

                            if (Segment["segmenttype"].ToString() == "hold")
                                TotalHold = TotalHold + 1;
                        }

                        if (CallBackPart > 0 && int.Parse(GenCodeSplit[0]) > CallBackPart)
                        {
                            CallBackPart = 0;
                            CallBackDetected = false;
                        }

                        LastDisconnect = Segment["disconnectiontype"].ToString();
                        LastPurpose = Segment["purpose"].ToString();

                        if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                        {
                            LastSegmentTime = Convert.ToDecimal(Segment["segmenttime"]);
                        }
                    }
                }

                ConvSummaryRow["conversationid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["keyid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["conversationstartdate"] = Conversation.Rows[0]["conversationstartdate"];
                ConvSummaryRow["conversationenddate"] = Conversation.Rows[0]["conversationenddate"];
                ConvSummaryRow["conversationstartdateltc"] = Conversation.Rows[0]["conversationstartdateltc"];
                ConvSummaryRow["conversationenddateltc"] = Conversation.Rows[0]["conversationenddateltc"];
                ConvSummaryRow["originaldirection"] = Conversation.Rows[0]["originaldirection"];
                ConvSummaryRow["firstmediatype"] = Conversation.Rows[0]["mediatype"];
                ConvSummaryRow["lastmediatype"] = LastMediaType;
                ConvSummaryRow["ani"] = ANI;
                ConvSummaryRow["dnis"] = DNIS;
                ConvSummaryRow["peer"] = LastPeer;
                ConvSummaryRow["firstagentid"] = FirstAgentId;
                ConvSummaryRow["lastagentid"] = LastAgentId;
                ConvSummaryRow["firstqueueid"] = FirstQueueId;
                ConvSummaryRow["lastqueueid"] = LastQueueId;
                ConvSummaryRow["ttalkcomplete"] = TalkTime;
                ConvSummaryRow["tqueuetime"] = QueueTime;
                ConvSummaryRow["tacw"] = WrapUpTime;
                ConvSummaryRow["tabandonedcount"] = AbandonCount;
                ConvSummaryRow["firstwrapupcode"] = FirstWrapUpCode;
                ConvSummaryRow["lastwrapupcode"] = LastWrapUpCode;
                ConvSummaryRow["theldcompletecount"] = TotalHold;
                ConvSummaryRow["theldcomplete"] = TotalHoldTime;
                ConvSummaryRow["thandlecount"] = HandleCount;
                ConvSummaryRow["thandle"] = TotalHandleTime;
                ConvSummaryRow["tansweredcount"] = AnswerCount;
                ConvSummaryRow["tanswered"] = TotalAnswerTime;
                ConvSummaryRow["tresponsecount"] = ResponseCount;
                ConvSummaryRow["tresponse"] = TotalResponseTime;
                ConvSummaryRow["nconsulttransferred"] = TotalWarmTrans;
                ConvSummaryRow["nblindtransferred"] = TotalColdTrans;
                ConvSummaryRow["lastdisconnect"] = LastDisconnect;
                ConvSummaryRow["lastpurpose"] = LastPurpose;
                ConvSummaryRow["lastsegmenttime"] = LastSegmentTime;
                ConvSummaryRow["divisionid"] = Conversation.Rows[0]["divisionid"];
                ConvSummaryRow["divisionid2"] = Conversation.Rows[0]["divisionid2"];
                ConvSummaryRow["divisionid3"] = Conversation.Rows[0]["divisionid3"];

                try
                {
                    ConversationSummaryData.Rows.Add(ConvSummaryRow);
                }
                catch (Exception ex)
                {
                    _logger?.LogDebug(ex, "Duplicate conversation summary detected for conversation {ConversationId}",
                        Conversation.Rows.Count > 0 ? Conversation.Rows[0]["conversationid"] : "unknown");
                }
            }

            return true;
        }

        public DataSet GetDetailInteractionDataFromGCQuery(String StartDate, String EndDate)
        {
            return GetDetailInteractionDataFromGCQueryAsync(StartDate, EndDate).GetAwaiter().GetResult();
        }

        public async Task<DataSet> GetDetailInteractionDataFromGCQueryAsync(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            Console.WriteLine("Retrieving Detail Interaction Data From Date:{0} ", StartDate);
            int PageNumber = 1;
            bool FoundData = true;

            Console.WriteLine("Creating Memory Table Detailed Interaction");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");

            Console.WriteLine("Creating Memory Table Participant Summary Dynamic");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Creating Memory Table Flow Outcome Data");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            while (FoundData)
            {
                _logger?.LogDebug("Processing page {PageNumber} for conversation details query", PageNumber);
                string RequestBody = "{" +
                                " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                " \"order\": \"asc\"," +
                                " \"orderBy\": \"conversationEnd\"," +
                                " \"paging\": {" +
                                " \"pageSize\": 100," +
                                "  \"pageNumber\": " + PageNumber.ToString() +
                                "}}";

                _logger?.LogDebug("Conversation details query request body: {RequestBody}", RequestBody);

                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/query", GCApiKey, RequestBody);

                // Check for null or empty response to prevent NullReferenceException
                if (string.IsNullOrEmpty(JsonString) || JsonString.Length < 50)
                {
                    if (string.IsNullOrEmpty(JsonString))
                    {
                        _logger?.LogWarning("Interactions: Received null or empty response from API");
                    }
                    else
                    {
                        _logger?.LogInformation("Interactions: No more data to process (response length: {Length})", JsonString.Length);
                    }
                    FoundData = false;
                    break;
                }

                Interactions.InteractionSegmentStruct DetailData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                foreach (Interactions.Conversation Conv in DetailData.conversations)
                {
                    int PartCode = 0;
                    int SessCode = 0;
                    int SegCode = 0;
                    foreach (Interactions.Participant ConvPart in Conv.participants)
                    {
                        DataRow CheckPartExists = ParticipantSummary.Select("keyid= '" + Conv.conversationId + "|" + ConvPart.participantId + "'").FirstOrDefault();

                        DataRow DRPartSumm = ParticipantSummary.NewRow();
                        DRPartSumm["keyid"] = Conv.conversationId + "|" + ConvPart.participantId;
                        DRPartSumm["conversationid"] = Conv.conversationId;
                        DRPartSumm["participantid"] = ConvPart.participantId;
                        DRPartSumm["purpose"] = ConvPart.purpose;

                        if (Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                        {
                            DRPartSumm["divisionid"] = Conv.divisionIds[0];
                        }
                        else
                        {
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }

                        if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                        {
                            _logger?.LogDebug("Setting default division ID for participant summary in query method");
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }
                        if (Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                            DRPartSumm["divisionid2"] = Conv.divisionIds[1];

                        if (Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                            DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                        // Conv.conversationStart is already UTC from API deserialization
                        DRPartSumm["conversationstartdate"] = Conv.conversationStart;
                        DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                        if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                        {
                            // Conv.conversationEnd is already UTC from API deserialization
                            DRPartSumm["conversationenddate"] = Conv.conversationEnd;
                            DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                        }
                        else
                        {
                            DRPartSumm["conversationenddate"] = System.DBNull.Value;
                            DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                        }

                        PartCode++;
                        SessCode = 0;
                        SegCode = 0;

                        foreach (Interactions.Session ConvSess in ConvPart.sessions)
                        {
                            SessCode++;
                            SegCode = 0;
                            Interactions.Flow ConvSessFlow = ConvSess.flow;
                            int SegmentCount = ConvSess.segments.Length;
                            int CurrentSegment = 1;

                            foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                            {
                                SegCode++;

                                if (!timeFieldsMillisecondResolution)
                                {
                                    ConvSeg.segmentStart = new DateTime(
                                          ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                          ConvSeg.segmentStart.Kind
                                        );

                                    ConvSeg.segmentEnd = new DateTime(
                                        ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                        ConvSeg.segmentEnd.Kind
                                     );
                                }

                                string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                                DataRow NewRow = DetailInteraction.NewRow();
                                string TempKeyid = Conv.conversationId + "|" + IterationCode;
                                NewRow["keyid"] = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);
                                NewRow["conversationid"] = Conv.conversationId;

                                if (Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                                {
                                    NewRow["divisionid"] = Conv.divisionIds[0];
                                }
                                else
                                {
                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }

                                if (NewRow["divisionid"] == null || NewRow["divisionid"] == System.DBNull.Value|| NewRow["divisionid"] is DBNull)
                                {
                                    _logger?.LogDebug("Setting default division ID for detailed interaction in query method");
                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }
                                if (Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                                    NewRow["divisionid2"] = Conv.divisionIds[1];
                                if (Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                                    NewRow["divisionid3"] = Conv.divisionIds[2];

                                // Conv.conversationStart is already UTC from API deserialization
                                NewRow["conversationstartdate"] = Conv.conversationStart;
                                NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                                if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                {
                                    // Conv.conversationEnd is already UTC from API deserialization
                                    NewRow["conversationenddate"] = Conv.conversationEnd;
                                    NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                                }
                                else
                                {
                                    NewRow["conversationenddate"] = System.DBNull.Value;
                                    NewRow["conversationenddateltc"] = System.DBNull.Value;
                                }

                                NewRow["gencode"] = IterationCode;
                                NewRow["peer"] = ConvSess.peerId;

                                DateTime MaxDateTest = Conv.conversationStart;
                                if (MaxDateTest > DetailInteractionLastUpdate)
                                {
                                    DetailInteractionLastUpdate = MaxDateTest;
                                    Console.Write("@");
                                }

                                NewRow["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2);
                                NewRow["originaldirection"] = Conv.originatingDirection;
                                NewRow["participantid"] = ConvPart.participantId;
                                if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                    NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                else
                                    NewRow["participantname"] = ConvPart.participantName;
                                NewRow["purpose"] = ConvPart.purpose;

                                NewRow["mediatype"] = ConvSess.mediaType;
                                DRPartSumm["mediaType"] = ConvSess.mediaType;

                                if (ConvSess.ani != null && ConvSess.ani.Length > 300)
                                    NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                else
                                    NewRow["ani"] = ConvSess.ani;

                                if (ConvSeg.queueId != null)
                                {
                                    NewRow["queueid"] = ConvSeg.queueId;
                                    DRPartSumm["queueid"] = ConvSeg.queueId;
                                }
                                if (ConvPart.userId != null)
                                {
                                    NewRow["userid"] = ConvPart.userId;
                                    DRPartSumm["userid"] = ConvPart.userId;
                                }

                                if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                    NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                else
                                    NewRow["dnis"] = ConvSess.dnis;

                                if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                    NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                else
                                    NewRow["sessiondnis"] = ConvSess.sessionDnis;

                                NewRow["sessiondirection"] = ConvSess.direction;
                                NewRow["edgeId"] = ConvSess.edgeId;
                                if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                    NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                else
                                    NewRow["remotedisplayable"] = ConvSess.remote;

                                NewRow["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2);

                                if (Conv.externalTag != null)
                                    NewRow["externalTag"] = Conv.externalTag;

                                // ConvSeg.segmentStart is already UTC from API deserialization
                                var segmentStartUtc = ConvSeg.segmentStart;
                                NewRow["segmentstartdate"] = segmentStartUtc;
                                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);

                                TimeSpan Diff = new TimeSpan();

                                if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                                {
                                    // ConvSeg.segmentEnd is already UTC from API deserialization
                                    var segmentEndUtc = ConvSeg.segmentEnd;
                                    NewRow["segmentenddate"] = segmentEndUtc;
                                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                                    Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                                    NewRow["segmenttime"] = Diff.TotalSeconds;
                                    Diff = ConvSeg.segmentEnd - Conv.conversationStart;
                                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                }
                                else
                                {
                                    NewRow["segmentenddate"] = System.DBNull.Value;
                                    NewRow["segmenttime"] = System.DBNull.Value;
                                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                }

                                Diff = ConvSeg.segmentStart - Conv.conversationStart;
                                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                                NewRow["segmenttype"] = ConvSeg.segmentType;
                                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                                string RowWrapUp = ConvSeg.wrapUpCode;
                                string RowWrapUpNote = ConvSeg.wrapUpNote;
                                if (RowWrapUp != null)
                                {
                                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                    NewRow["wrapupcode"] = RowWrapUp;
                                    DRPartSumm["wrapupcode"] = RowWrapUp;
                                    if (RowWrapUpNote != null)
                                    {
                                        NewRow["wrapupnote"] = RowWrapUpNote;
                                        DRPartSumm["wrapupnote"] = RowWrapUpNote;
                                    }
                                    else
                                    {
                                        NewRow["wrapupnote"] = "";
                                    }
                                }
                                else
                                {
                                    NewRow["wrapupcode"] = "";
                                    NewRow["wrapupnote"] = "";
                                }

                                if (ConvSeg.disconnectType == null)
                                    NewRow["disconnectiontype"] = "none";
                                else
                                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                NewRow["sessionprovider"] = ConvSess.provider;

                                if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                {
                                    NewRow["flowid"] = ConvSessFlow.flowId;
                                    NewRow["flowname"] = ConvSessFlow.flowName;

                                    try
                                    {
                                        NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                    }
                                    catch
                                    {
                                        NewRow["flowversion"] = 1.0;
                                    }

                                    NewRow["flowtype"] = ConvSessFlow.flowType;
                                    NewRow["exitreason"] = ConvSessFlow.exitReason;
                                    NewRow["entryreason"] = ConvSessFlow.entryReason;
                                    NewRow["entrytype"] = ConvSessFlow.entryType;
                                    NewRow["transfertype"] = ConvSessFlow.transferType;
                                    if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                                        NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                                    else
                                        NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                                    NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                    // Use the centralized FlowOutcomeProcessor for consistent processing
                                    if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                    {
                                        try
                                        {
                                            var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);
                                            var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                new[] { ConvSessFlow },
                                                Conv.conversationId,
                                                Conv.conversationStart,
                                                Conv.conversationEnd,
                                                FlowOutcomes,
                                                throwOnInvalidDates: false); // Use false to match the original graceful error handling

                                            // Add "F" to status string to indicate flow outcome processing
                                            if (flowOutcomeResult.TotalProcessed > 0)
                                            {
                                                Console.Write("F");
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                                                Conv.conversationId, ex.Message);
                                            // Continue processing other conversations
                                        }
                                    }
                                }

                                if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                {
                                    foreach (Interactions.Metric ConvSessMetric in ConvSess.metrics)
                                    {
                                        string FirstChar = ConvSessMetric.name.Substring(0, 1);
                                        try
                                        {
                                            switch (FirstChar)
                                            {
                                                case "n":
                                                    if (ConvSessMetric.value > 0)
                                                    {
                                                        // Check if column exists before setting value
                                                        if (DetailInteraction.Columns.Contains(ConvSessMetric.name))
                                                        {
                                                            NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                        }
                                                        else
                                                        {
                                                            _logger?.LogDebug("Column '{MetricName}' does not exist in detailedInteractionData table. Skipping metric.", ConvSessMetric.name);
                                                        }

                                                        // Check if column exists in participant summary table before setting value
                                                        if (ParticipantSummary.Columns.Contains(ConvSessMetric.name))
                                                        {
                                                            DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                        }
                                                        else
                                                        {
                                                            _logger?.LogDebug("Column '{MetricName}' does not exist in participantsummaryData table. Skipping metric.", ConvSessMetric.name);
                                                        }
                                                    }
                                                    break;
                                                case "t":
                                                    if (ConvSessMetric.value > 0)
                                                    {
                                                        if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                            ConvSessMetric.value += 100;

                                                        decimal calculatedValue;
                                                        if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                        {
                                                            calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11m;
                                                        }
                                                        else
                                                        {
                                                            calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                        }

                                                        // Check if column exists before setting value
                                                        if (DetailInteraction.Columns.Contains(ConvSessMetric.name))
                                                        {
                                                            NewRow[ConvSessMetric.name] = calculatedValue;
                                                        }
                                                        else
                                                        {
                                                            _logger?.LogDebug("Column '{MetricName}' does not exist in detailedInteractionData table. Skipping metric.", ConvSessMetric.name);
                                                        }

                                                        // Check if column exists in participant summary table before setting value
                                                        if (ParticipantSummary.Columns.Contains(ConvSessMetric.name))
                                                        {
                                                            DRPartSumm[ConvSessMetric.name] = calculatedValue;
                                                        }
                                                        else
                                                        {
                                                            _logger?.LogDebug("Column '{MetricName}' does not exist in participantsummaryData table. Skipping metric.", ConvSessMetric.name);
                                                        }
                                                    }
                                                    break;
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            _logger?.LogWarning(e, "Error processing metric '{MetricName}' with value {MetricValue}. Error: {ErrorMessage}",
                                                ConvSessMetric.name, ConvSessMetric.value, e.Message);
                                            Console.WriteLine("Error processing metric '{0}' with value {1}. Error: {2}", ConvSessMetric.name, ConvSessMetric.value, e.Message);
                                        }
                                    }
                                }

                                try
                                {
                                    DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + NewRow["keyid"] + "'").FirstOrDefault();
                                    if (CheckRowExists == null)
                                    {
                                        DetailInteraction.Rows.Add(NewRow);
                                        // Record added - using debug level to avoid log flooding
                                    }
                                    else
                                    {
                                        // Duplicate record found - using debug level to avoid log flooding
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("Exception caught in Interaction Detail Module.\nError Message: {0}\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                                    throw;
                                }

                                CurrentSegment++;
                                // Segment processed - using debug level to avoid log flooding
                            }
                        }

                        if (CheckPartExists == null)
                        {
                            ParticipantSummary.Rows.Add(DRPartSumm);
                            // Participant added - using debug level to avoid log flooding
                        }
                        else
                        {
                            // Participant duplicate found - using debug level to avoid log flooding
                        }
                    }
                }

                PageNumber++;
            }

            Console.WriteLine("\nReturning {0} Row(s)", DetailInteraction.Rows.Count);

            // Get participant attributes for the conversations retrieved in QUERY mode
            Console.WriteLine("Retrieving Participant Attribute Data for QUERY mode");
            DataTable ParticipantAttributes = await GetParticipantAttributesAsync(DetailInteraction);
            ParticipantAttributes.TableName = "participantAttributesDynamic";

            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantAttributes);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);

            return ReturnInteractionData;
        }

        public DataTable GetParticipantAttributes(DataTable InteractionData)
        {
            return GetParticipantAttributesAsync(InteractionData).GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetParticipantAttributesAsync(DataTable InteractionData)
        {
            // Add comprehensive null checking at the start
            if (InteractionData == null)
            {
                _logger?.LogError("InteractionData parameter is null in GetParticipantAttributes");
                throw new ArgumentNullException(nameof(InteractionData), "InteractionData cannot be null");
            }

            if (GCControlData?.Tables?["GCControlData"]?.Rows == null || GCControlData.Tables["GCControlData"].Rows.Count == 0)
            {
                _logger?.LogError("GCControlData is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("GCControlData is not properly initialized");
            }

            if (string.IsNullOrEmpty(TimeZoneConfig))
            {
                _logger?.LogError("TimeZoneConfig is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("TimeZoneConfig is not properly configured");
            }

            int MaxNVarCharLength = 0;
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    MaxNVarCharLength = 200;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    MaxNVarCharLength = 50;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    MaxNVarCharLength = 100;
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            Console.WriteLine("Retrieving Participant Attribute Data");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"]?.ToString();
            if (string.IsNullOrEmpty(URI))
            {
                _logger?.LogError("GC_URL is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("GC_URL is not properly configured in GCControlData");
            }

            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            DataTable SmallConvTable = CreateSmallConversationDetails();

            // Use configurable lookback period instead of hardcoded 92 days
            int lookbackDays = 92; // TODO: Make this configurable from settings
            DateTime cutoffDate = DateTime.UtcNow.AddDays(-lookbackDays);

            // Use HashSet for efficient duplicate detection instead of string comparison
            HashSet<string> processedConversationIds = new HashSet<string>();

            // Filter conversations using LINQ for better performance and safety
            var recentConversations = InteractionData.AsEnumerable()
                .Where(row => row.Field<DateTime>("conversationstartdate") > cutoffDate)
                .ToList();

            _logger?.LogDebug("Processing {ConversationCount} conversations from the last {LookbackDays} days",
                recentConversations.Count, lookbackDays);

            foreach (DataRow Conversation in recentConversations)
            {
                // Capture conversation ID once to avoid threading issues
                string currentConversationId = Conversation["conversationId"]?.ToString();

                // Skip if conversation ID is null or already processed
                if (string.IsNullOrEmpty(currentConversationId) ||
                    !processedConversationIds.Add(currentConversationId))
                {
                    continue;
                }

                DataRow SmallConvRow = SmallConvTable.NewRow();
                SmallConvRow["conversationid"] = currentConversationId;
                SmallConvRow["mediatype"] = Conversation["mediatype"];
                SmallConvRow["conversationstartdate"] = Conversation["conversationstartdate"];
                SmallConvRow["conversationenddate"] = Conversation["conversationenddate"];
                SmallConvRow["conversationstartdateltc"] = Conversation["conversationstartdateltc"];
                SmallConvRow["conversationenddateltc"] = Conversation["conversationenddateltc"];
                SmallConvTable.Rows.Add(SmallConvRow);
            }

            Console.WriteLine("\nFinished Getting Conversation IDs. Count {0}", SmallConvTable.Rows.Count);

            // Process conversations in async batches for better performance
            await ProcessParticipantAttributesAsync(SmallConvTable, ParticipantAttributes, URI, GCApiKey, MaxNVarCharLength);

            Console.WriteLine("Number of Part Rows Returning: {0} ", ParticipantAttributes.Rows.Count);
            return ParticipantAttributes;
        }

        private async Task ProcessParticipantAttributesAsync(DataTable SmallConvTable, DataTable ParticipantAttributes, string URI, string GCApiKey, int MaxNVarCharLength)
        {
            const int BATCH_SIZE = 50;
            int MAX_CONCURRENT_TASKS = Environment.ProcessorCount; // Limit concurrent API calls to prevent rate limiting

            var conversationRows = SmallConvTable.Rows.Cast<DataRow>().ToList();
            _logger?.LogInformation("Processing {TotalCount} conversations for participant attributes in async batches of {BatchSize}",
                conversationRows.Count, BATCH_SIZE);

            // Create a semaphore to limit concurrent tasks
            using var throttler = new SemaphoreSlim(MAX_CONCURRENT_TASKS);
            var processingTasks = new List<Task>();

            int processedCount = 0;
            var lockObject = new object();

            // Process conversations in batches
            for (int i = 0; i < conversationRows.Count; i += BATCH_SIZE)
            {
                var batch = conversationRows.Skip(i).Take(BATCH_SIZE).ToList();

                // Wait for a slot to become available
                await throttler.WaitAsync();

                // Create a task for this batch
                processingTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await ProcessConversationBatch(batch, ParticipantAttributes, URI, GCApiKey, MaxNVarCharLength, lockObject);

                        // Update progress counter thread-safely
                        int currentCount;
                        lock (lockObject)
                        {
                            processedCount += batch.Count;
                            currentCount = processedCount;
                        }

                        // Log progress every 100 conversations
                        if (currentCount % 100 == 0 || currentCount == conversationRows.Count)
                        {
                            _logger?.LogDebug("Processed {ProcessedCount}/{TotalCount} participant attributes ({Percent:P0})",
                                currentCount, conversationRows.Count, (double)currentCount / conversationRows.Count);
                        }
                    }
                    finally
                    {
                        throttler.Release();
                    }
                }));
            }

            // Wait for all batches to complete
            await Task.WhenAll(processingTasks);
            _logger?.LogInformation("Completed processing all {TotalCount} conversations for participant attributes", conversationRows.Count);
        }

        private async Task ProcessConversationBatch(List<DataRow> conversationBatch, DataTable ParticipantAttributes, string URI, string GCApiKey, int MaxNVarCharLength, object lockObject)
        {
            int processedCount = 0;
            int failedCount = 0;
            int duplicateCount = 0;

            foreach (var SmallConvRow in conversationBatch)
            {
                try
                {
                    // Add null check for SmallConvRow and conversationid
                    if (SmallConvRow == null || SmallConvRow["conversationid"] == null || SmallConvRow["conversationid"] == System.DBNull.Value)
                    {
                        _logger?.LogWarning("SmallConvRow or conversationid is null. Skipping this conversation.");
                        continue;
                    }

                    string currentConversationId = SmallConvRow["conversationid"].ToString();
                    if (string.IsNullOrEmpty(currentConversationId))
                    {
                        _logger?.LogWarning("conversationid is empty. Skipping this conversation.");
                        failedCount++;
                        continue;
                    }

                    // Check if we already have a row for this conversation to avoid duplicates
                    bool conversationExists = false;
                    lock (lockObject)
                    {
                        conversationExists = ParticipantAttributes.AsEnumerable()
                            .Any(row => row.Field<string>("conversationid") == currentConversationId);
                    }

                    if (conversationExists)
                    {
                        _logger?.LogDebug("Conversation {ConversationId} already exists in ParticipantAttributes. Skipping to avoid duplicates.",
                            currentConversationId);
                        duplicateCount++;
                        continue;
                    }

                    string media = "calls"; // Default to calls
                    if (SmallConvRow["mediatype"] != null && SmallConvRow["mediatype"] != System.DBNull.Value)
                    {
                        string mediaType = SmallConvRow["mediatype"].ToString();
                        if (!string.IsNullOrEmpty(mediaType))
                        {
                            switch (mediaType)
                            {
                                case "voice":
                                    media = "calls";
                                    break;
                                default:
                                    media = mediaType + "s";
                                    break;
                            }
                        }
                    }
                    else
                    {
                        _logger?.LogWarning("mediatype is null or DBNull for conversation {ConversationId}. Using default 'calls'.",
                            SmallConvRow["conversationid"]);
                    }

                    // Use the modern rate-limited HTTP method that handles token refresh automatically
                    string JsonString = await JsonActions.JsonReturnStringGetAsync(URI + "/api/v2/conversations/" + media + "/" + currentConversationId, GCApiKey);

                    if (!string.IsNullOrEmpty(JsonString) && JsonString.Length > 10)
                    {
                        PartAttribs.ParticipantAttributes DetailData = JsonConvert.DeserializeObject<PartAttribs.ParticipantAttributes>(JsonString,
                                          new JsonSerializerSettings
                                          {
                                              NullValueHandling = NullValueHandling.Ignore
                                          });

                        if (DetailData != null)
                        {
                            DataRow DRPartAttrib = null;
                            try
                            {
                                DRPartAttrib = ParticipantAttributes.NewRow();

                                // Add null checks for SmallConvRow access and capture conversation ID once to avoid threading issues
                                if (SmallConvRow["conversationid"] != null && SmallConvRow["conversationid"] != System.DBNull.Value)
                                {
                                    // Capture the conversation ID once to ensure consistency across keyid and conversationid fields
                                    // This prevents threading issues where SmallConvRow["conversationid"] could change between reads
                                    string capturedConversationId = SmallConvRow["conversationid"].ToString();
                                    DRPartAttrib["keyid"] = capturedConversationId;
                                    DRPartAttrib["conversationid"] = capturedConversationId;
                                }
                                else
                                {
                                    _logger?.LogWarning("conversationid is null or DBNull in SmallConvRow. Skipping participant attributes for this conversation.");
                                    continue;
                                }

                                if (SmallConvRow["conversationstartdate"] != null && SmallConvRow["conversationstartdate"] != System.DBNull.Value)
                                    DRPartAttrib["conversationstartdate"] = SmallConvRow["conversationstartdate"];

                                if (SmallConvRow["conversationstartdateltc"] != null && SmallConvRow["conversationstartdateltc"] != System.DBNull.Value)
                                    DRPartAttrib["conversationstartdateltc"] = SmallConvRow["conversationstartdateltc"];

                                if (SmallConvRow["conversationenddate"] != null && SmallConvRow["conversationenddate"] != System.DBNull.Value)
                                {
                                    if (Convert.ToDateTime(SmallConvRow["conversationenddate"]) > DateTime.UtcNow.AddYears(-20))
                                    {
                                        DRPartAttrib["conversationenddate"] = SmallConvRow["conversationenddate"];
                                        if (SmallConvRow["conversationenddateltc"] != null && SmallConvRow["conversationenddateltc"] != System.DBNull.Value)
                                            DRPartAttrib["conversationenddateltc"] = SmallConvRow["conversationenddateltc"];
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "Failed to create or populate DataRow for participant attributes in conversation {ConversationId}. Skipping this conversation.",
                                    SmallConvRow?["conversationid"]);
                                continue;
                            }

                            // Add null check for participants collection
                            if (DetailData.participants != null)
                            {
                                foreach (PartAttribs.Participant ConvPart in DetailData.participants)
                                {
                                    // Add null check for participant
                                    if (ConvPart == null)
                                    {
                                        _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.",
                                            SmallConvRow["conversationid"]);
                                        continue;
                                    }

                                    if (ConvPart.attributes != null)
                                    {
                                        Dictionary<string, string> values = null;
                                        try
                                        {
                                            string attributesJson = ConvPart.attributes?.ToString();
                                            if (!string.IsNullOrWhiteSpace(attributesJson))
                                            {
                                                values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogWarning(ex, "Failed to deserialize participant attributes for conversation {ConversationId}. Skipping attributes processing.",
                                                SmallConvRow["conversationid"]);
                                            continue; // Skip this participant's attributes and move to the next
                                        }

                                        if (values == null)
                                        {
                                            _logger?.LogDebug("No valid participant attributes found for conversation {ConversationId}",
                                                SmallConvRow["conversationid"]);
                                            continue; // Skip this participant's attributes and move to the next
                                        }

                                        // Thread-safe column addition
                                        lock (lockObject)
                                        {
                                            DataColumnCollection columns = ParticipantAttributes.Columns;

                                            foreach (KeyValuePair<string, string> kvp in values)
                                            {
                                                if (!string.IsNullOrEmpty(kvp.Key))
                                                {
                                                    string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                                    // Add null check for ConParamName
                                                    if (ConParamName == null)
                                                    {
                                                        _logger?.LogWarning("RenameParticipantAttributeNames returned null for key '{Key}' in conversation {ConversationId}. Skipping attribute.",
                                                            kvp.Key, SmallConvRow["conversationid"]);
                                                        continue;
                                                    }

                                                    if (ConParamName.Length <= 55)
                                                        ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                    else if (ConParamName.Length >= 55)
                                                        ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                    if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                        ConParamName = ConParamName.ToLower();

                                                    // Add null check before using ConParamName
                                                    if (!string.IsNullOrEmpty(ConParamName) && !columns.Contains(ConParamName))
                                                    {
                                                        ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                        Console.WriteLine("Adding Column:{0} as {1}", kvp.Key, ConParamName);
                                                    }
                                                }
                                            }

                                            ParticipantAttributes.AcceptChanges();
                                        }

                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            string ConParamName = String.Empty;
                                            string AttribValue = String.Empty;
                                            string AttribName = String.Empty;

                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                                if (ConParamName == null)
                                                {
                                                    _logger?.LogWarning("RenameParticipantAttributeNames returned null for key '{Key}' in conversation {ConversationId}. Skipping attribute assignment.",
                                                        kvp.Key, SmallConvRow["conversationid"]);
                                                    continue;
                                                }

                                                if (ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (ConParamName.Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                    ConParamName = ConParamName.ToLower();

                                                switch (DBUtil.DBType)
                                                {
                                                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                        AttribName = ConParamName;
                                                        break;
                                                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                        if (!string.IsNullOrEmpty(ConParamName))
                                                        {
                                                            if (ConParamName.Length <= 55)
                                                                AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                            else
                                                                AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        }
                                                        break;
                                                    default:
                                                        throw new NotImplementedException("Database type is not implemented");
                                                }

                                                // Add null check for AttribName before using it
                                                if (!string.IsNullOrEmpty(AttribName))
                                                {
                                                    AttribValue = kvp.Value ?? "";
                                                    if (AttribValue.Length > MaxNVarCharLength)
                                                        AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                                    try
                                                    {
                                                        DRPartAttrib[AttribName] = AttribValue;
                                                    }
                                                    catch (ArgumentException ex)
                                                    {
                                                        _logger?.LogWarning(ex, "Failed to set attribute '{AttribName}' with value '{AttribValue}' for conversation {ConversationId}. Column may not exist.",
                                                            AttribName, AttribValue, SmallConvRow["conversationid"]);
                                                    }
                                                }
                                                else
                                                {
                                                    _logger?.LogWarning("AttribName is null or empty for key '{Key}' in conversation {ConversationId}. Skipping attribute assignment.",
                                                        kvp.Key, SmallConvRow["conversationid"]);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                _logger?.LogDebug("No participants found for conversation {ConversationId} in GetParticipantAttributes",
                                    SmallConvRow["conversationid"]);
                            }

                            // Add the row with proper error handling (thread-safe)
                            if (DRPartAttrib != null)
                            {
                                lock (lockObject)
                                {
                                    try
                                    {
                                        ParticipantAttributes.Rows.Add(DRPartAttrib);
                                        processedCount++;
                                        // Participant attribute added - using debug level to avoid log flooding
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger?.LogError(ex, "Failed to add participant attributes row for conversation {ConversationId}. Row data may be invalid.",
                                            SmallConvRow?["conversationid"]);
                                        failedCount++;
                                    }
                                }
                            }
                            else
                            {
                                _logger?.LogWarning("DRPartAttrib is null for conversation {ConversationId}. Cannot add to ParticipantAttributes table.",
                                    SmallConvRow?["conversationid"]);
                                failedCount++;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error processing conversation {ConversationId} in GetParticipantAttributes. Continuing with next conversation.",
                        SmallConvRow?["conversationid"]);
                    failedCount++;
                    // Continue processing other conversations instead of failing the entire job
                    continue;
                }
            }

            // Log batch processing summary
            _logger?.LogInformation("Completed processing conversation batch: {ProcessedCount} successful, {FailedCount} failed, {DuplicateCount} duplicates skipped, {TotalCount} total",
                processedCount, failedCount, duplicateCount, conversationBatch.Count);
        }

        public DataTable CreateSmallConversationDetails()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.Columns.Add("conversationid", typeof(String));
            DTTemp.Columns.Add("conversationstartdate", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddate", typeof(DateTime));
            DTTemp.Columns.Add("conversationstartdateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationminmos", typeof(decimal));
            DTTemp.Columns.Add("conversationminrfactor", typeof(decimal));
            DTTemp.Columns.Add("originaldirection", typeof(String));
            DTTemp.Columns.Add("mediatype", typeof(String));
            DTTemp.Columns.Add("ani", typeof(String));
            DTTemp.Columns.Add("dnis", typeof(String));
            DTTemp.Columns.Add("remoteName", typeof(String));

            DTTemp.TableName = "simpleInteractionData";

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;
        }

        private int ConvertBoolean(bool Inbound)
        {
            return Inbound ? 1 : 0;
        }

        private int DetermineMaxNVarCharLength()
        {
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    return 200;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    return 50;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    return 100;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }

        public async Task<DataSet> GetDetailInteractionDataFromGCJob(String StartDate, String EndDate)
        {
            int MaxNVarCharLength = DetermineMaxNVarCharLength();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            Console.WriteLine("Using timezone: {0}", TimeZoneConfig);
            Console.WriteLine("Data retrieval window: {0} to {1}", StartDate, EndDate);

            DataSet DSDetailInteraction = new DataSet();
            Console.WriteLine("Initializing detailed interaction data table");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");
            bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);

            Console.WriteLine("Initializing participant attributes data table");
            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            Console.WriteLine("Initializing participant summary data table");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            Console.WriteLine("Initializing flow outcome data table");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            Console.WriteLine("\nRetrieving detailed interaction data starting from: {0}", StartDate);

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";

            string RequestBody = "{" +
                                 " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                 " \"order\": \"asc\"," +
                                 " \"orderBy\": \"conversationEnd\"" +
                                 "}";

            Console.WriteLine("API request body:\n{0}", RequestBody);
            var apiResponse = JsonActions.JsonReturnHttpResponse(URI + "/api/v2/analytics/conversations/details/jobs", GCApiKey, RequestBody);

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                Console.WriteLine("\nError: Empty response received from API - aborting data retrieval");
                return DSDetailInteraction;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess && !apiResponse.IsAccepted)
            {
                _logger?.LogError("API Error in GetDetailInteractionDataFromGCJob: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types that should halt processing
                if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                {
                    _logger?.LogError("Critical API error detected (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                    throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode}: {apiResponse.Content}");
                }

                // For other errors, return empty dataset to indicate failure
                Console.WriteLine("\nError: API returned error response - aborting data retrieval");
                return DSDetailInteraction;
            }

            DetInt.ReportJob? JobID = null;
            try
            {
                JobID = JsonConvert.DeserializeObject<DetInt.ReportJob>(apiResponse.Content,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });
            }
            catch (JsonException jsonEx)
            {
                // Include problematic JSON in the error message for debugging
                string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                _logger?.LogError(jsonEx, "JSON Deserialization Error in GetDetailInteractionDataFromGCJob. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                    apiResponse.StatusCode, jsonPreview);
                throw;
            }

            if (JobID == null)
            {
                Console.WriteLine("Error: Failed to parse job ID from API response");
                return DSDetailInteraction;
            }

            // Wait for job completion using polling
            _logger?.LogInformation("Waiting for job {JobId} completion via polling", JobID.jobId);
            var jobCompletionResult = await WaitForJobCompletionViaPollingAsync(URI, JobID.jobId);

            if (!jobCompletionResult.Success)
            {
                _logger?.LogError("Interactions: Job {JobId} failed - {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);
                Console.WriteLine($"\nInteractions: Job Failed - {jobCompletionResult.ErrorMessage}");

                // If the error is related to a critical failure, we should throw an exception
                // to make sure the job doesn't exit with a success code
                if (jobCompletionResult.ErrorMessage.Contains("Timeout waiting for job") ||
                    jobCompletionResult.ErrorMessage.Contains("Error waiting for job completion"))
                {
                    _logger?.LogError("Failed to poll job {JobId} status: {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);
                    throw new InvalidOperationException($"Failed to poll job {JobID.jobId} status: {jobCompletionResult.ErrorMessage}");
                }

                return DSDetailInteraction;
            }

            _logger?.LogInformation("Job {JobId} completed successfully via polling", JobID.jobId);

            Console.WriteLine("\nInteractions: Job ID:{0} Status:FULFILLED", JobID.jobId);

            string LastCursor = String.Empty;
            int cursorLoopCount=0;
            List<DetInt.DetailedInteractions> detailedInteractionsList = new List<DetInt.DetailedInteractions>();

            var detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
            while (detailedInteractions != null && detailedInteractions.conversations != null && detailedInteractions.conversations.Count() > 0)
            {
                Console.WriteLine("Retrieving data page {1} with cursor: {0}", detailedInteractions.cursor, cursorLoopCount);
                detailedInteractionsList.Add(detailedInteractions);
                if (string.IsNullOrEmpty(detailedInteractions.cursor))
                    break;
                LastCursor = detailedInteractions.cursor;
                detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
                cursorLoopCount++;
            }

            // Flatten all conversations into one list
            var allConversations = detailedInteractionsList
                .Where(d => d.conversations != null && d.conversations.Count() > 0)
                .SelectMany(d => d.conversations)
                .ToList();

            int batchSize = 50000;
            if (allConversations.Count > 0)
            {
                var batches = allConversations
                    .Select((Conv, index) => new { Conv, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(g => g.Select(x => x.Conv).ToList())
                    .ToList();
                Console.WriteLine($"Processing data in {batches.Count} batches");

                bool result = await ProcessDataAsync(batches, DetailInteraction, ParticipantSummary, ParticipantAttributes, FlowOutcomes, AppTimeZone, timeFieldsMillisecondResolution, MaxNVarCharLength);
                Console.WriteLine("All data batches processed successfully");
            }
            else
            {
                _logger?.LogInformation("No conversation data returned from API");
            }

            DSDetailInteraction.Tables.Add(DetailInteraction);
            DSDetailInteraction.Tables.Add(ParticipantAttributes);
            DSDetailInteraction.Tables.Add(ParticipantSummary);
            DSDetailInteraction.Tables.Add(FlowOutcomes);
            _logger?.LogInformation("Latest conversation date found: {LatestDate}", DetailInteractionLastUpdate);

            return DSDetailInteraction;
        }

        private async Task<DetInt.DetailedInteractions> FetchDataAsync(string uri, string apiKey, string jobID, string LastCursor)
        {
            try
            {
                string CursorString = string.IsNullOrEmpty(LastCursor) ? "" : "?cursor=" + HttpUtility.UrlEncode(LastCursor);
                string url = $"{uri}/api/v2/analytics/conversations/details/jobs/{jobID}/results{CursorString}";

                var apiResponse = await JsonActions.JsonReturnHttpResponseGetAsync(url, apiKey);

                // Validate response using proper HTTP status code detection
                if (string.IsNullOrWhiteSpace(apiResponse.Content))
                {
                    _logger?.LogWarning("Empty response received in FetchDataAsync - returning null");
                    return null;
                }

                // Handle different HTTP status codes appropriately
                if (!apiResponse.IsSuccess)
                {
                    _logger?.LogError("API Error in FetchDataAsync: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                        apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                    // Check for specific error types that should halt processing
                    if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                    {
                        _logger?.LogError("Critical API error detected in FetchDataAsync (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                        throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode} in FetchDataAsync: {apiResponse.Content}");
                    }

                    // For other errors, return null to indicate failure
                    _logger?.LogWarning("Returning null from FetchDataAsync due to HTTP {StatusCode} error", apiResponse.StatusCode);
                    return null;
                }

                DetInt.DetailedInteractions detailData = null;
                try
                {
                    detailData = JsonConvert.DeserializeObject<DetInt.DetailedInteractions>(apiResponse.Content,
                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    // Include problematic JSON in the error message for debugging
                    string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                    _logger?.LogError(jsonEx, "JSON Deserialization Error in FetchDataAsync. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                        apiResponse.StatusCode, jsonPreview);
                    throw;
                }

                return detailData;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving data from API in FetchDataAsync: {Message}", ex.Message);
                Console.WriteLine($"Error retrieving data from API: {ex.Message}");
                return null;
            }
        }

        private async Task<bool> ProcessDataAsync(
            IEnumerable<IEnumerable<DetInt.Conversation>> batches,
            DataTable DetailInteraction,
            DataTable ParticipantSummary,
            DataTable ParticipantAttributes,
            DataTable FlowOutcomes,
            TimeZoneInfo AppTimeZone,
            bool timeFieldsMillisecondResolution,
            int MaxNVarCharLength)
        {
            var taskExceptions = new ConcurrentBag<Exception>();

            var detailInteractionRows = new ConcurrentBag<DataRow>();
            var participantSummaryRows = new ConcurrentBag<DataRow>();
            var participantAttributeRows = new ConcurrentBag<DataRow>();
            var flowOutcomesRows = new ConcurrentBag<DataRow>();

            int processedCount = 0;
            Stopwatch totalStopwatch = Stopwatch.StartNew();
            Stopwatch segmentStopwatch = Stopwatch.StartNew();

            // We will print a status message every time we process another 1000 records
            int printInterval = 100;

            var tasks = batches.Select(batch =>
                Task.Run(async () =>
                {
                    try
                    {
                        foreach (var Conv in batch)
                        {
                            // Check if we already have a row for this conversation to avoid duplicates
                            // This is critical because both ProcessDataAsync and ProcessParticipantAttributesAsync
                            // can be called in the same execution, leading to duplicate key constraint violations
                            bool conversationExists = participantAttributeRows.Any(row =>
                                row.Field<string>("conversationid") == Conv.conversationId) ||
                                ParticipantAttributes.AsEnumerable().Any(row =>
                                row.Field<string>("conversationid") == Conv.conversationId);

                            if (conversationExists)
                            {
                                _logger?.LogDebug("Conversation {ConversationId} already exists in ParticipantAttributes. Skipping to avoid duplicates in ProcessDataAsync.",
                                    Conv.conversationId);
                                continue;
                            }

                            DataRow DRPartAttrib = ParticipantAttributes.NewRow();
                            // Capture conversation ID once to ensure consistency (defensive programming)
                            string conversationId = Conv.conversationId;
                            DRPartAttrib["keyid"] = conversationId;
                            DRPartAttrib["conversationid"] = conversationId;
                            // Conv.conversationStart is already UTC from API deserialization
                            DRPartAttrib["conversationstartdate"] = Conv.conversationStart;
                            DRPartAttrib["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                            if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                            {
                                // Conv.conversationEnd is already UTC from API deserialization
                                DRPartAttrib["conversationenddate"] = Conv.conversationEnd;
                                DRPartAttrib["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                            }

                            int PartCode = 0;
                            if (Conv.participants != null)
                            {
                                foreach (var ConvPart in Conv.participants)
                                {
                                    // Add null check for participant
                                    if (ConvPart == null)
                                    {
                                        _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    // Add null check for critical participant properties
                                    if (string.IsNullOrEmpty(ConvPart.participantId))
                                    {
                                        _logger?.LogWarning("Participant with null or empty participantId found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    DataRow DRPartSumm = ParticipantSummary.NewRow();
                                    DRPartSumm["keyid"] = Conv.conversationId + "|" + ConvPart.participantId;
                                    DRPartSumm["conversationid"] = Conv.conversationId;
                                    DRPartSumm["participantid"] = ConvPart.participantId;
                                    DRPartSumm["purpose"] = ConvPart.purpose;

                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                                    {
                                        DRPartSumm["divisionid"] = Conv.divisionIds[0];
                                    }
                                    else
                                    {
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }

                                    if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                                    {
                                        _logger?.LogDebug("Setting default division ID for participant summary in async processing");
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                                        DRPartSumm["divisionid2"] = Conv.divisionIds[1];
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                                        DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                                    // Conv.conversationStart is already UTC from API deserialization
                                    DRPartSumm["conversationstartdate"] = Conv.conversationStart;
                                    DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                                    if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                    {
                                        // Conv.conversationEnd is already UTC from API deserialization
                                        DRPartSumm["conversationenddate"] = Conv.conversationEnd;
                                        DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                                    }
                                    else
                                    {
                                        DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                        DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                    }

                                    PartCode++;
                                    int SessCode = 0;
                                    int SegCode = 0;

                                    // Handle participant attributes safely
                                    if (ConvPart.attributes != null)
                                    {
                                        Dictionary<string, string> values = null;
                                        try
                                        {
                                            string attributesJson = ConvPart.attributes?.ToString();
                                            if (!string.IsNullOrWhiteSpace(attributesJson))
                                            {
                                                values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogWarning(ex, "Failed to deserialize participant attributes for conversation {ConversationId} in async processing. Skipping attributes processing.",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        if (values == null)
                                        {
                                            _logger?.LogDebug("No valid participant attributes found for conversation {ConversationId} in async processing",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        DataColumnCollection columns = ParticipantAttributes.Columns;

                                        // Add columns if needed
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                    ConParamName = ConParamName.ToLower();

                                                if (!columns.Contains(ConParamName))
                                                {
                                                    lock (_participantAttributeLock)
                                                    {
                                                        if (!columns.Contains(ConParamName))
                                                            ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                    }
                                                }
                                            }
                                        }

                                        // Assign attribute values
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            string ConParamName = String.Empty;
                                            string AttribValue = String.Empty;
                                            string AttribName = String.Empty;

                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                                if (ConParamName == null) continue;

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                    ConParamName = ConParamName.ToLower();

                                                switch (DBUtil.DBType)
                                                {
                                                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                        AttribName = ConParamName;
                                                        break;
                                                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                        if (ConParamName != null && ConParamName.Length <= 55)
                                                            AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        else if (kvp.Value != null && ConParamName.Length >= 55)
                                                            AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        break;
                                                default:
                                                    throw new NotImplementedException("Database type is not implemented");
                                                }

                                                AttribValue = kvp.Value ?? "";
                                                if (AttribValue.Length > MaxNVarCharLength)
                                                    AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                                DRPartAttrib[AttribName] = AttribValue;
                                            }
                                        }
                                    }

                                    if (ConvPart.sessions != null)
                                    {
                                        foreach (DetInt.Session ConvSess in ConvPart.sessions)
                                        {
                                            SessCode++;
                                            SegCode = 0;
                                            //Console.WriteLine("Currently Looking at Session  :{0}", ConvSess.sessionId);

                                            DetInt.Flow ConvSessFlow = ConvSess.flow;
                                            int SegmentCount = ConvSess.segments.Length;
                                            int CurrentSegment = 1;

                                            foreach (DetInt.Segment ConvSeg in ConvSess.segments)
                                            {
                                                SegCode++;
                                                //Console.WriteLine("Currently Looking at Segment  :{0}", ConvSeg.segmentType);

                                                string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                                                DataRow NewRow = DetailInteraction.NewRow();
                                                string TempKeyid = Conv.conversationId + "|" + IterationCode;
                                                string rowKey = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);

                                                NewRow["keyid"] = rowKey;
                                                //NewRow["keyid"] = Conv.conversationId;
                                                NewRow["conversationid"] = Conv.conversationId;


                                                if (Conv.divisionIds[0] != null && Conv.divisionIds[0] != "")
                                                {
                                                    NewRow["divisionid"] = Conv.divisionIds[0];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                                }

                                                if (NewRow["divisionid"] == null
                                                    || NewRow["divisionid"] is DBNull
                                                    || (NewRow["divisionid"] is string str && string.IsNullOrWhiteSpace(str)))
                                                {
                                                    _logger?.LogDebug("Setting default division ID for detailed interaction in async processing");
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-000000000000";
                                                }

                                                if (Conv.divisionIds.Count() > 1 && Conv.divisionIds[1] != null && Conv.divisionIds[1] != "")
                                                {
                                                    NewRow["divisionid2"] = Conv.divisionIds[1];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid2"] = "";
                                                }
                                                if (Conv.divisionIds.Count() > 2 && Conv.divisionIds[2] != null && Conv.divisionIds[2] != "")
                                                {
                                                    NewRow["divisionid3"] = Conv.divisionIds[2];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid3"] = "";
                                                }
                                                // Conv.conversationStart is already UTC from API deserialization
                                                NewRow["conversationstartdate"] = Conv.conversationStart;
                                                NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                                                if (Conv.conversationEnd != null && Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    // Conv.conversationEnd is already UTC from API deserialization
                                                    NewRow["conversationenddate"] = Conv.conversationEnd;
                                                    NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                                                }

                                                NewRow["gencode"] = IterationCode;

                                                DateTime MaxDateTest = Conv.conversationStart;
                                                if (MaxDateTest > DetailInteractionLastUpdate)
                                                {
                                                    DetailInteractionLastUpdate = MaxDateTest;
                                                    // Console.Write("@");
                                                }

                                                NewRow["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2);
                                                NewRow["originaldirection"] = Conv.originatingDirection;
                                                NewRow["participantid"] = ConvPart.participantId;
                                                NewRow["peer"] = ConvSess.peerId;
                                                if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                                    NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                                else
                                                    NewRow["participantname"] = ConvPart.participantName;
                                                NewRow["purpose"] = ConvPart.purpose;
                                                NewRow["mediatype"] = ConvSess.mediaType;

                                                DRPartSumm["mediaType"] = ConvSess.mediaType;

                                                if (ConvSess.ani != null && ConvSess.ani.Length > 300
                                                                        && (ConvSess.ani.IndexOf("tel:") > 0 || ConvSess.ani.IndexOf("sip:") > 0))
                                                    NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                                else
                                                    NewRow["ani"] = ConvSess.ani;

                                                if (ConvSeg.queueId != null)
                                                {
                                                    NewRow["queueid"] = ConvSeg.queueId;
                                                    DRPartSumm["queueid"] = ConvSeg.queueId;
                                                }
                                                if (ConvPart.userId != null)
                                                {
                                                    NewRow["userid"] = ConvPart.userId;
                                                    DRPartSumm["userid"] = ConvPart.userId;
                                                }



                                                if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                                    NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                                else
                                                    NewRow["dnis"] = ConvSess.dnis;


                                                if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                                else
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis;


                                                NewRow["sessiondirection"] = ConvSess.direction;
                                                NewRow["edgeId"] = ConvSess.edgeId;
                                                if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                                    NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                                else
                                                    NewRow["remotedisplayable"] = ConvSess.remote;

                                                NewRow["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2);
                                                if (Conv.externalTag != null)
                                                    NewRow["externalTag"] = Conv.externalTag;

                                                if (! timeFieldsMillisecondResolution)
                                                {
                                                    ConvSeg.segmentStart = new DateTime(
                                                        ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentStart.Kind
                                                    );

                                                    ConvSeg.segmentEnd = new DateTime(
                                                        ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentEnd.Kind
                                                    );
                                                }
                                                // ConvSeg.segmentStart is already UTC from API deserialization
                                                var segmentStartUtc = ConvSeg.segmentStart;
                                                NewRow["segmentstartdate"] = segmentStartUtc;
                                                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);


                                                System.TimeSpan Diff = new System.TimeSpan();

                                                if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    // ConvSeg.segmentEnd is already UTC from API deserialization
                                                    var segmentEndUtc = ConvSeg.segmentEnd;
                                                    NewRow["segmentenddate"] = segmentEndUtc;
                                                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                                                    Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                                                    NewRow["segmenttime"] = Diff.TotalSeconds;
                                                    Diff = ConvSeg.segmentEnd - Conv.conversationStart;
                                                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                                }
                                                else
                                                {
                                                    NewRow["segmentenddate"] = System.DBNull.Value;
                                                    NewRow["segmenttime"] = System.DBNull.Value;
                                                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                                }

                                                Diff = ConvSeg.segmentStart - Conv.conversationStart;
                                                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;


                                                NewRow["segmenttype"] = ConvSeg.segmentType;
                                                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;


                                                string RowWrapUp = ConvSeg.wrapUpCode;
                                                string RowWrapUpNote = ConvSeg.wrapUpNote;

                                                if (RowWrapUp != null)
                                                {
                                                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                                    NewRow["wrapupcode"] = RowWrapUp;
                                                    DRPartSumm["wrapupcode"] = RowWrapUp;
                                                    if (RowWrapUpNote != null)
                                                    {
                                                        NewRow["wrapupnote"]= ConvSeg.wrapUpNote;
                                                        DRPartSumm["wrapupnote"] =ConvSeg.wrapUpNote;
                                                    }
                                                    else
                                                    {
                                                        NewRow["wrapupnote"]= "";
                                                    }

                                                }
                                                else
                                                {
                                                    NewRow["wrapupcode"] = "";
                                                    NewRow["wrapupnote"]= "";
                                                }

                                                if (ConvSeg.disconnectType == null)
                                                    NewRow["disconnectiontype"] = "none";
                                                else
                                                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                                NewRow["sessionprovider"] = ConvSess.provider;


                                                if (CurrentSegment == SegmentCount)
                                                {
                                                    //Insert Flow Data into last Segment
                                                    if (ConvSessFlow != null)
                                                    {
                                                        if (ConvSessFlow.flowId != null)
                                                        {
                                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                                            if (ConvSessFlow.flowName != null && ConvSessFlow.flowName.Length > 254)
                                                                NewRow["flowname"] = ConvSessFlow.flowName.Substring(0, 254);
                                                            else
                                                                NewRow["flowname"] = ConvSessFlow.flowName;
                                                            try
                                                            {
                                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                            }
                                                            catch
                                                            {
                                                                NewRow["flowversion"] = 1.0;
                                                            }
                                                            NewRow.SetFieldValue(rowKey, "flowtype", ConvSessFlow.flowType);
                                                            NewRow.SetFieldValue(rowKey, "exitreason", ConvSessFlow.exitReason);
                                                            NewRow.SetFieldValue(rowKey, "entryreason", ConvSessFlow.entryReason);
                                                            NewRow.SetFieldValue(rowKey, "entrytype", ConvSessFlow.entryType);
                                                            NewRow.SetFieldValue(rowKey, "transfertype", ConvSessFlow.transferType);
                                                            if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                                                            else
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                                            // Use the centralized FlowOutcomeProcessor for consistent processing
                                                            if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                                            {
                                                                try
                                                                {
                                                                    var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);

                                                                    // Create a temporary DataTable to collect flow outcomes for this conversation
                                                                    var tempFlowOutcomes = FlowOutcomes.Clone();

                                                                    var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                                        new[] { ConvSessFlow },
                                                                        Conv.conversationId,
                                                                        Conv.conversationStart,
                                                                        Conv.conversationEnd,
                                                                        tempFlowOutcomes,
                                                                        throwOnInvalidDates: true); // Use true to match the original strict error handling

                                                                    // Add the processed rows to the thread-safe collection
                                                                    foreach (DataRow row in tempFlowOutcomes.Rows)
                                                                    {
                                                                        flowOutcomesRows.Add(row);
                                                                    }

                                                                    // Add "F" to status string to indicate flow outcome processing
                                                                    // Note: In async processing, we don't use Console.Write due to thread safety concerns
                                                                    // The flow outcome processing is tracked through the overall progress indicators
                                                                    if (flowOutcomeResult.TotalProcessed > 0)
                                                                    {
                                                                        // Flow outcomes processed - status tracked via overall progress metrics
                                                                    }
                                                                }
                                                                catch (Exception ex)
                                                                {
                                                                    _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                                                                        Conv.conversationId, ex.Message);
                                                                    // Re-throw to maintain existing behavior for this implementation
                                                                    throw;
                                                                }
                                                            }
                                                        }
                                                    }

                                                    if (ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                                    {

                                                        foreach (DetInt.Metric ConvSessMetric in ConvSess.metrics)
                                                        {
                                                            string FirstChar = ConvSessMetric.name.Substring(0, 1);

                                                            try
                                                            {
                                                                // Check if the column exists in both tables before attempting assignment
                                                                bool detailColumnExists = DetailInteraction.Columns.Contains(ConvSessMetric.name);
                                                                bool summaryColumnExists = ParticipantSummary.Columns.Contains(ConvSessMetric.name);

                                                                if (!detailColumnExists && !summaryColumnExists)
                                                                {
                                                                    _logger?.LogDebug("Metric column '{MetricName}' not found in database schema - skipping assignment", ConvSessMetric.name);
                                                                    continue;
                                                                }

                                                                switch (FirstChar)
                                                                {
                                                                    case "n":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (detailColumnExists)
                                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                            if (summaryColumnExists)
                                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                                        }
                                                                        break;
                                                                    case "t":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                                ConvSessMetric.value = ConvSessMetric.value + 100;

                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                            {
                                                                                var adjustedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = adjustedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = adjustedValue;
                                                                            }
                                                                            else
                                                                            {
                                                                                var roundedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = roundedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = roundedValue;
                                                                            }
                                                                        }
                                                                        break;
                                                                }
                                                            }
                                                            catch (Exception ex)
                                                            {
                                                                _logger?.LogWarning(ex, "Failed to assign metric '{MetricName}' with value {MetricValue}", ConvSessMetric.name, ConvSessMetric.value);
                                                            }

                                                        }
                                                    }

                                                    //if (ConvSess.metrics != null || ConvSessFlow != null)
                                                    //    Console.Write("%");
                                                }
                                                detailInteractionRows.Add(NewRow);

                                                CurrentSegment++;
                                                // Console.Write("#");
                                            }
                                        }
                                    }

                                    participantSummaryRows.Add(DRPartSumm);
                                }
                            }

                            // Add participant attributes row with proper error handling
                            try
                            {
                                participantAttributeRows.Add(DRPartAttrib);
                            }
                            catch (System.Data.ConstraintException ex)
                            {
                                _logger?.LogDebug(ex, "Duplicate participant attributes detected for conversation {ConversationId} in ProcessDataAsync", Conv.conversationId);
                                // Continue processing - this is expected when the same conversation is processed multiple times
                            }

                            // Increment the processed counter for every conversation
                            int localCount = Interlocked.Increment(ref processedCount);

                            // Every 1000 records, print a status
                            if (localCount % printInterval == 0)
                            {
                                var elapsed = segmentStopwatch.Elapsed;
                                _logger?.LogInformation("Processing progress: {ProcessedCount} records processed in {ElapsedSeconds:F2} seconds",
                                    localCount, elapsed.TotalSeconds);
                                segmentStopwatch.Restart(); // reset the segment stopwatch
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        taskExceptions.Add(ex);
                    }
                })
            ).ToList();

            try
            {
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                taskExceptions.Add(ex);
            }

            if (taskExceptions.Count > 0)
            {
                Console.WriteLine("Error: Exceptions occurred during data processing:");
                foreach (var ex in taskExceptions)
                {
                    Console.WriteLine(ex.ToString());
                }
                return false;
            }

            totalStopwatch.Stop();

            Console.WriteLine("Merging processed data into database tables...");

            lock (_detailInteractionLock)
            {
                foreach (var row in detailInteractionRows)
                {
                    try
                    {
                        DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckRowExists == null)
                            DetailInteraction.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to detailed interaction table: {0}", ex.Message);
                    }
                }
            }

            lock (_participantSummaryLock)
            {
                foreach (var row in participantSummaryRows)
                {
                    try
                    {
                        DataRow CheckPartSumm = ParticipantSummary.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartSumm == null)
                            ParticipantSummary.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to participant summary table: {0}", ex.Message);
                    }
                }
            }

            lock (_participantAttributeLock)
            {
                foreach (var row in participantAttributeRows)
                {
                    try
                    {
                        DataRow CheckPartAttrib = ParticipantAttributes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartAttrib == null)
                            ParticipantAttributes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error adding row to participant attributes table: {0}", ex.Message);
                    }
                }
            }

            lock (_flowOutcomesLock)
            {
                foreach (var row in flowOutcomesRows)
                {
                    try
                    {
                        DataRow CheckFlowOutcome = FlowOutcomes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckFlowOutcome == null)
                            FlowOutcomes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error adding row to flow outcomes table");
                    }
                }
            }

            // Log flow outcome processing summary
            if (flowOutcomesRows.Count > 0)
            {
                _logger?.LogInformation("Flow outcome processing completed: {FlowOutcomeCount} flow outcomes processed", flowOutcomesRows.Count);
            }
            else
            {
                _logger?.LogInformation("Flow outcome processing completed: No flow outcomes found in API response data");
            }

            _logger?.LogInformation("All conversation data processed successfully. Processing summary: {ProcessedCount} total interactions processed in {ElapsedSeconds:F2} seconds",
                processedCount, totalStopwatch.Elapsed.TotalSeconds);

            return true;
        }

    }

    public class JobDateLimit
    {
        public DateTime dataAvailabilityDate { get; set; }
    }
}
